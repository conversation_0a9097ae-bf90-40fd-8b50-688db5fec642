apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: aetherforge
    user: aetherforge
    secureJsonData:
      password: ${POSTGRES_PASSWORD}
    jsonData:
      sslmode: "disable"
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
