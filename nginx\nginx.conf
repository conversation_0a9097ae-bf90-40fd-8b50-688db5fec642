events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=ui:10m rate=30r/s;

    # Upstream definitions
    upstream orchestrator {
        server orchestrator:8000;
        keepalive 32;
    }

    upstream archon {
        server archon:8100;
        keepalive 32;
    }

    upstream archon_ui {
        server archon:8501;
        keepalive 32;
    }

    upstream mcp_crawl4ai {
        server mcp-crawl4ai:8051;
        keepalive 32;
    }

    upstream pheromind {
        server pheromind:8502;
        keepalive 32;
    }

    upstream pheromind_ui {
        server pheromind:3000;
        keepalive 32;
    }

    upstream bmad {
        server bmad:8503;
        keepalive 32;
    }

    upstream grafana {
        server grafana:3000;
        keepalive 32;
    }

    upstream prometheus {
        server prometheus:9090;
        keepalive 32;
    }

    # Main server block
    server {
        listen 80;
        server_name localhost aetherforge.local;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        # Main Aetherforge UI (if we had one)
        location / {
            return 301 /orchestrator/;
        }

        # Orchestrator API
        location /orchestrator/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://orchestrator/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Archon API
        location /archon/api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://archon/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Archon Streamlit UI
        location /archon/ {
            limit_req zone=ui burst=50 nodelay;
            
            proxy_pass http://archon_ui/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support for Streamlit
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # MCP-Crawl4AI API
        location /mcp/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://mcp_crawl4ai/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Pheromind API
        location /pheromind/api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://pheromind/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Pheromind UI
        location /pheromind/ {
            limit_req zone=ui burst=50 nodelay;
            
            proxy_pass http://pheromind_ui/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # BMAD API
        location /bmad/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://bmad/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Monitoring - Grafana
        location /grafana/ {
            auth_basic "Aetherforge Monitoring";
            auth_basic_user_file /etc/nginx/.htpasswd;
            
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Monitoring - Prometheus
        location /prometheus/ {
            auth_basic "Aetherforge Monitoring";
            auth_basic_user_file /etc/nginx/.htpasswd;
            
            proxy_pass http://prometheus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Static files for generated projects (if needed)
        location /projects/ {
            alias /var/www/projects/;
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
        }
    }

    # HTTPS server (uncomment and configure SSL certificates for production)
    # server {
    #     listen 443 ssl http2;
    #     server_name aetherforge.local;
    #
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #
    #     # Include the same location blocks as above
    # }
}
