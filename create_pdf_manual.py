#!/usr/bin/env python3
"""
Convert Aetherforge User Manual from Markdown to PDF
"""

import os
import sys
import re
from pathlib import Path
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY


def parse_markdown_content(content):
    """Parse markdown content into sections"""
    sections = []
    lines = content.split('\n')

    current_section = None
    current_content = []

    for line in lines:
        line = line.strip()

        if line.startswith('# '):
            if current_section:
                sections.append(current_section)
            current_section = {'type': 'h1', 'content': line[2:]}
            current_content = []

        elif line.startswith('## '):
            if current_section:
                sections.append(current_section)
            current_section = {'type': 'h2', 'content': line[3:]}
            current_content = []

        elif line.startswith('### '):
            if current_section:
                sections.append(current_section)
            current_section = {'type': 'h3', 'content': line[4:]}
            current_content = []

        elif line.startswith('```'):
            if current_section and current_section['type'] == 'code':
                current_section['content'] = '\n'.join(current_content)
                sections.append(current_section)
                current_section = None
                current_content = []
            else:
                if current_section:
                    sections.append(current_section)
                current_section = {'type': 'code', 'content': ''}
                current_content = []

        elif current_section and current_section['type'] == 'code':
            current_content.append(line)

        elif line.startswith('- ') or line.startswith('* '):
            if current_section and current_section['type'] != 'list':
                sections.append(current_section)
                current_section = {'type': 'list', 'items': []}
            if not current_section:
                current_section = {'type': 'list', 'items': []}
            current_section['items'].append(line[2:])

        elif line and not line.startswith('#'):
            if current_section and current_section['type'] == 'list':
                sections.append(current_section)
                current_section = None
            if not current_section or current_section['type'] != 'paragraph':
                if current_section:
                    sections.append(current_section)
                current_section = {'type': 'paragraph', 'content': line}
            else:
                current_section['content'] += ' ' + line

    if current_section:
        sections.append(current_section)

    return sections


def create_pdf_manual():
    """Convert the markdown manual to PDF using ReportLab"""

    # Read the markdown file
    markdown_file = Path("AETHERFORGE_USER_MANUAL.md")
    if not markdown_file.exists():
        print("❌ AETHERFORGE_USER_MANUAL.md not found!")
        return False

    print("📖 Reading markdown file...")
    with open(markdown_file, 'r', encoding='utf-8') as f:
        markdown_content = f.read()

    # Parse markdown content
    print("🔄 Parsing markdown content...")
    sections = parse_markdown_content(markdown_content)

    # Create PDF
    print("📄 Creating PDF...")
    pdf_file = "AETHERFORGE_USER_MANUAL.pdf"

    # Create document
    doc = SimpleDocTemplate(
        pdf_file,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=18
    )

    # Get styles
    styles = getSampleStyleSheet()

    # Create custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.HexColor('#2c3e50')
    )

    heading1_style = ParagraphStyle(
        'CustomHeading1',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.HexColor('#2c3e50')
    )

    heading2_style = ParagraphStyle(
        'CustomHeading2',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=10,
        spaceBefore=15,
        textColor=colors.HexColor('#34495e')
    )

    heading3_style = ParagraphStyle(
        'CustomHeading3',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.HexColor('#2c3e50')
    )

    code_style = ParagraphStyle(
        'Code',
        parent=styles['Code'],
        fontSize=9,
        fontName='Courier',
        backColor=colors.HexColor('#f8f9fa'),
        borderColor=colors.HexColor('#e9ecef'),
        borderWidth=1,
        borderPadding=5
    )

    # Build story
    story = []

    # Add title page
    story.append(Paragraph("🔮 Aetherforge", title_style))
    story.append(Spacer(1, 12))
    story.append(Paragraph("User Manual", title_style))
    story.append(Spacer(1, 12))
    story.append(Paragraph("Version 1.0.0", styles['Normal']))
    story.append(Spacer(1, 6))
    story.append(Paragraph("June 19, 2025", styles['Normal']))
    story.append(PageBreak())

    # Process sections
    for section in sections:
        if section['type'] == 'h1':
            story.append(PageBreak())
            story.append(Paragraph(section['content'], heading1_style))
        elif section['type'] == 'h2':
            story.append(Paragraph(section['content'], heading2_style))
        elif section['type'] == 'h3':
            story.append(Paragraph(section['content'], heading3_style))
        elif section['type'] == 'code':
            story.append(Paragraph(section['content'], code_style))
            story.append(Spacer(1, 6))
        elif section['type'] == 'paragraph':
            story.append(Paragraph(section['content'], styles['Normal']))
            story.append(Spacer(1, 6))
        elif section['type'] == 'list':
            for item in section['items']:
                story.append(Paragraph(f"• {item}", styles['Normal']))
            story.append(Spacer(1, 6))

    # Build PDF
    try:
        doc.build(story)
        print(f"✅ PDF created successfully: {pdf_file}")

        # Get file size
        file_size = os.path.getsize(pdf_file) / (1024 * 1024)  # MB
        print(f"📊 File size: {file_size:.2f} MB")

        return True
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        return False


def main():
    """Main function"""
    print("🔮 Aetherforge PDF Manual Generator")
    print("=" * 50)

    if create_pdf_manual():
        print("\n🎉 PDF manual created successfully!")
        print("📁 File: AETHERFORGE_USER_MANUAL.pdf")
        print("\nYou can now:")
        print("  • Download the PDF file")
        print("  • Share it with your team")
        print("  • Print it for offline reference")
        print("  • Use it as comprehensive documentation")
    else:
        print("\n❌ Failed to create PDF manual")
        sys.exit(1)


if __name__ == "__main__":
    main()
