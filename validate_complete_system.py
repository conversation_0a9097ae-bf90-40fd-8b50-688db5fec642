#!/usr/bin/env python3
"""
Complete Aetherforge System Validation
Tests all components and validates the complete implementation
"""

import os
import sys
import subprocess
import json
import asyncio
import time
import requests
from pathlib import Path
from datetime import datetime
import tempfile
import shutil

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 70)
    print(f"  {title}")
    print("=" * 70)

def print_status(message, status="INFO"):
    """Print a status message"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    status_symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅",
        "ERROR": "❌",
        "WARNING": "⚠️",
        "RUNNING": "🔄"
    }
    symbol = status_symbols.get(status, "•")
    print(f"[{timestamp}] {symbol} {message}")

def check_python_dependencies():
    """Check if required Python dependencies are available"""
    print_header("Checking Python Dependencies")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "requests",
        "openai",
        "aiohttp",
        "python-slugify",
        "structlog",
        "python-dotenv"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print_status(f"{package} - available", "SUCCESS")
        except ImportError:
            print_status(f"{package} - missing", "ERROR")
            missing_packages.append(package)
    
    if missing_packages:
        print_status(f"Install missing packages: pip install {' '.join(missing_packages)}", "WARNING")
        return False
    
    print_status("All Python dependencies satisfied", "SUCCESS")
    return True

def check_environment_variables():
    """Check required environment variables"""
    print_header("Checking Environment Variables")
    
    required_vars = ["OPENAI_API_KEY"]
    optional_vars = ["ANTHROPIC_API_KEY", "ORCHESTRATOR_URL"]
    
    env_ok = True
    
    for var in required_vars:
        if var in os.environ and os.environ[var]:
            print_status(f"{var} - configured", "SUCCESS")
        else:
            print_status(f"{var} - missing (required)", "ERROR")
            env_ok = False
    
    for var in optional_vars:
        if var in os.environ and os.environ[var]:
            print_status(f"{var} - configured", "SUCCESS")
        else:
            print_status(f"{var} - using default", "WARNING")
    
    return env_ok

def test_orchestrator_startup():
    """Test orchestrator startup and basic functionality"""
    print_header("Testing Orchestrator Startup")
    
    try:
        print_status("Starting orchestrator...", "RUNNING")
        
        # Start orchestrator
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "src.orchestrator:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for startup
        time.sleep(8)
        
        # Test health endpoint
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print_status(f"Health check passed: {health_data.get('status', 'unknown')}", "SUCCESS")
                
                # Test components status
                components_response = requests.get("http://localhost:8000/components/status", timeout=5)
                if components_response.status_code == 200:
                    print_status("Components status endpoint working", "SUCCESS")
                
                # Test pheromone statistics
                pheromone_response = requests.get("http://localhost:8000/pheromones/statistics", timeout=5)
                if pheromone_response.status_code == 200:
                    stats = pheromone_response.json()
                    print_status(f"Pheromone system active: {stats.get('statistics', {}).get('total_messages', 0)} messages", "SUCCESS")
                
                # Test API documentation
                docs_response = requests.get("http://localhost:8000/docs", timeout=5)
                if docs_response.status_code == 200:
                    print_status("API documentation accessible", "SUCCESS")
                
                # Cleanup
                process.terminate()
                process.wait(timeout=5)
                
                return True
            else:
                print_status(f"Health check failed: {response.status_code}", "ERROR")
                
        except requests.exceptions.RequestException as e:
            print_status(f"Failed to connect to orchestrator: {e}", "ERROR")
        
        # Cleanup
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        return False
        
    except Exception as e:
        print_status(f"Error testing orchestrator: {e}", "ERROR")
        return False

def test_project_creation():
    """Test end-to-end project creation"""
    print_header("Testing Project Creation")
    
    try:
        print_status("Starting orchestrator for project creation test...", "RUNNING")
        
        # Start orchestrator
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "src.orchestrator:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for startup
        time.sleep(8)
        
        # Create test project
        project_data = {
            "prompt": "Create a simple todo list application with user authentication",
            "project_name": "TestTodoApp",
            "project_type": "fullstack",
            "workflow": "greenfield-fullstack"
        }
        
        print_status("Creating test project...", "RUNNING")
        
        response = requests.post(
            "http://localhost:8000/projects",
            json=project_data,
            timeout=120  # 2 minutes for project creation
        )
        
        if response.status_code == 200:
            result = response.json()
            project_id = result.get("project_id")
            print_status(f"Project creation started: {project_id}", "SUCCESS")
            
            # Wait for project completion
            print_status("Waiting for project completion...", "RUNNING")
            time.sleep(30)  # Wait 30 seconds for processing
            
            # Check project status
            projects_response = requests.get("http://localhost:8000/projects", timeout=10)
            if projects_response.status_code == 200:
                projects = projects_response.json()
                test_project = next((p for p in projects if p.get("project_id") == project_id), None)
                
                if test_project:
                    status = test_project.get("status", "unknown")
                    print_status(f"Test project status: {status}", "SUCCESS")
                    
                    # Check if project files were created
                    project_path = test_project.get("project_path")
                    if project_path and os.path.exists(project_path):
                        files_created = []
                        for root, dirs, files in os.walk(project_path):
                            files_created.extend(files)
                        
                        print_status(f"Project files created: {len(files_created)} files", "SUCCESS")
                        
                        # Check for key files
                        key_files = ["README.md", "package.json", ".aetherforge.json"]
                        for key_file in key_files:
                            if os.path.exists(os.path.join(project_path, key_file)):
                                print_status(f"Key file present: {key_file}", "SUCCESS")
                            else:
                                print_status(f"Key file missing: {key_file}", "WARNING")
                    
                else:
                    print_status("Test project not found in project list", "WARNING")
            
            # Cleanup
            process.terminate()
            process.wait(timeout=5)
            
            return True
        else:
            print_status(f"Project creation failed: {response.status_code} - {response.text}", "ERROR")
        
        # Cleanup
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        return False
        
    except Exception as e:
        print_status(f"Error testing project creation: {e}", "ERROR")
        return False

def test_pheromone_system():
    """Test pheromone communication system"""
    print_header("Testing Pheromone System")
    
    try:
        # Import and test pheromone system directly
        sys.path.insert(0, 'src')
        from pheromone_system import RealTimePheromoneSystem
        
        # Create test system
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = os.path.join(temp_dir, "test_pheromones.json")
            pheromone_system = RealTimePheromoneSystem(test_file)
            
            async def test_pheromones():
                # Start system
                await pheromone_system.start()
                print_status("Pheromone system started", "SUCCESS")
                
                # Test message dropping
                message_id = await pheromone_system.drop_pheromone(
                    "test_message", 
                    {"test": "data"}, 
                    "test_project_123"
                )
                print_status(f"Pheromone dropped: {message_id}", "SUCCESS")
                
                # Test statistics
                stats = pheromone_system.get_statistics()
                print_status(f"System stats: {stats['total_messages']} messages", "SUCCESS")
                
                # Test project messages
                project_messages = pheromone_system.get_project_messages("test_project_123")
                print_status(f"Project messages: {len(project_messages)}", "SUCCESS")
                
                # Stop system
                await pheromone_system.stop()
                print_status("Pheromone system stopped", "SUCCESS")
                
                return True
            
            # Run async test
            result = asyncio.run(test_pheromones())
            return result
        
    except Exception as e:
        print_status(f"Error testing pheromone system: {e}", "ERROR")
        return False

def test_configuration_system():
    """Test configuration management"""
    print_header("Testing Configuration System")
    
    try:
        sys.path.insert(0, 'src')
        from config_manager import ConfigurationManager
        
        # Create test configuration
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = os.path.join(temp_dir, "test_config.json")
            config_manager = ConfigurationManager(config_file)
            
            # Test default configuration
            print_status(f"Default workflows: {len(config_manager.workflows)}", "SUCCESS")
            print_status(f"Default agents: {len(config_manager.agents)}", "SUCCESS")
            print_status(f"Default tech stacks: {len(config_manager.tech_stacks)}", "SUCCESS")
            
            # Test workflow retrieval
            workflow = config_manager.get_workflow("greenfield-fullstack")
            if workflow:
                print_status(f"Workflow retrieved: {workflow.name}", "SUCCESS")
            else:
                print_status("Failed to retrieve workflow", "ERROR")
                return False
            
            # Test configuration saving
            config_manager.save_configuration()
            if os.path.exists(config_file):
                print_status("Configuration saved successfully", "SUCCESS")
            else:
                print_status("Configuration save failed", "ERROR")
                return False
            
            return True
        
    except Exception as e:
        print_status(f"Error testing configuration system: {e}", "ERROR")
        return False

def generate_validation_report(results):
    """Generate comprehensive validation report"""
    print_header("Validation Report")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print_status(f"Total Tests: {total_tests}", "INFO")
    print_status(f"Passed: {passed_tests}", "SUCCESS" if passed_tests == total_tests else "INFO")
    print_status(f"Failed: {failed_tests}", "ERROR" if failed_tests > 0 else "INFO")
    print_status(f"Success Rate: {success_rate:.1f}%", "SUCCESS" if success_rate >= 80 else "WARNING")
    
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        symbol = "✅" if result else "❌"
        print(f"  {symbol} {test_name}: {status}")
    
    # Generate JSON report
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": success_rate
        },
        "results": results,
        "system_info": {
            "python_version": sys.version,
            "platform": sys.platform,
            "working_directory": os.getcwd()
        }
    }
    
    report_file = Path("aetherforge_validation_report.json")
    report_file.write_text(json.dumps(report, indent=2))
    print_status(f"Validation report saved to {report_file}", "INFO")
    
    return success_rate >= 80

def main():
    """Main validation function"""
    print_header("🔮 Aetherforge Complete System Validation")
    print_status("Starting comprehensive system validation", "INFO")
    
    # Track all test results
    results = {}
    
    # Run all validation tests
    results["python_dependencies"] = check_python_dependencies()
    results["environment_variables"] = check_environment_variables()
    
    if results["python_dependencies"] and results["environment_variables"]:
        results["orchestrator_startup"] = test_orchestrator_startup()
        results["pheromone_system"] = test_pheromone_system()
        results["configuration_system"] = test_configuration_system()
        results["project_creation"] = test_project_creation()
    else:
        print_status("Skipping integration tests due to dependency/environment issues", "WARNING")
        results["orchestrator_startup"] = False
        results["pheromone_system"] = False
        results["configuration_system"] = False
        results["project_creation"] = False
    
    # Generate comprehensive report
    success = generate_validation_report(results)
    
    if success:
        print_header("🎉 Validation Complete - System Ready!")
        print_status("Aetherforge is fully operational and ready for use", "SUCCESS")
        print_status("You can now create autonomous software projects!", "SUCCESS")
        return 0
    else:
        print_header("❌ Validation Failed")
        print_status("Some components need attention before full operation", "ERROR")
        print_status("Please review the validation report and fix issues", "WARNING")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
