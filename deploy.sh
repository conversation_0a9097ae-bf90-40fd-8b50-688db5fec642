#!/bin/bash

# Aetherforge Deployment Script
# This script sets up and deploys the complete Aetherforge system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-development}
COMPOSE_FILE="docker-compose.yml"

if [ "$ENVIRONMENT" = "production" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
fi

echo -e "${BLUE}🚀 Aetherforge Deployment Script${NC}"
echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}Compose file: $COMPOSE_FILE${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi
print_status "Docker is installed"

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi
print_status "Docker Compose is installed"

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        print_status "Created .env file from .env.example"
        print_warning "Please edit .env file with your configuration before continuing"
        echo "Press Enter to continue after editing .env file..."
        read
    else
        print_error ".env.example file not found. Please create .env file manually."
        exit 1
    fi
else
    print_status ".env file exists"
fi

# Create necessary directories
echo -e "${BLUE}📁 Creating directories...${NC}"
mkdir -p projects
mkdir -p components/Archon/data
mkdir -p components/mcp-crawl4ai-rag/data
mkdir -p components/Pheromind/data
mkdir -p components/BMAD-METHOD/workflows
mkdir -p monitoring/grafana/dashboards/json
mkdir -p nginx/ssl
print_status "Directories created"

# Copy component directories if they don't exist
echo -e "${BLUE}📦 Setting up components...${NC}"

setup_component() {
    local component=$1
    local source_dir=$2
    
    if [ ! -d "components/$component" ]; then
        if [ -d "$source_dir" ]; then
            print_status "Copying $component from $source_dir"
            cp -r "$source_dir" "components/$component"
        else
            print_warning "$component source directory not found at $source_dir"
            print_warning "Creating placeholder for $component"
            mkdir -p "components/$component"
            echo "# $component Component" > "components/$component/README.md"
        fi
    else
        print_status "$component component directory exists"
    fi
}

setup_component "Archon" "Archon-main/Archon-main"
setup_component "mcp-crawl4ai-rag" "mcp-crawl4ai-rag-main/mcp-crawl4ai-rag-main"
setup_component "Pheromind" "Pheromind-main/Pheromind-main"
setup_component "BMAD-METHOD" "BMAD-METHOD-main/BMAD-METHOD-main"

# Build and start services
echo -e "${BLUE}🔨 Building and starting services...${NC}"

# Stop any existing services
print_status "Stopping existing services..."
docker-compose -f $COMPOSE_FILE down --remove-orphans

# Build images
print_status "Building Docker images..."
docker-compose -f $COMPOSE_FILE build --no-cache

# Start services
print_status "Starting services..."
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to be ready
echo -e "${BLUE}⏳ Waiting for services to be ready...${NC}"
sleep 30

# Health checks
echo -e "${BLUE}🏥 Performing health checks...${NC}"

check_service() {
    local service=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_status "$service is healthy"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service health check failed"
    return 1
}

echo "Checking service health..."
check_service "Orchestrator" "http://localhost:8000/health"
check_service "PostgreSQL" "http://localhost:5432" || print_warning "PostgreSQL check skipped (no HTTP endpoint)"
check_service "Redis" "http://localhost:6379" || print_warning "Redis check skipped (no HTTP endpoint)"

# Display service status
echo -e "${BLUE}📊 Service Status:${NC}"
docker-compose -f $COMPOSE_FILE ps

# Display access information
echo ""
echo -e "${GREEN}🎉 Aetherforge deployment completed!${NC}"
echo ""
echo -e "${BLUE}📍 Service Access Points:${NC}"
echo "• Orchestrator API: http://localhost:8000"
echo "• Orchestrator Health: http://localhost:8000/health"
echo "• Orchestrator Docs: http://localhost:8000/docs"
echo "• Archon API: http://localhost:8100"
echo "• Archon UI: http://localhost:8501"
echo "• MCP-Crawl4AI: http://localhost:8051"
echo "• Pheromind: http://localhost:8502"
echo "• BMAD-METHOD: http://localhost:8503"
echo "• PostgreSQL: localhost:5432"
echo "• Redis: localhost:6379"

if [ "$ENVIRONMENT" = "production" ]; then
    echo "• Grafana: http://localhost:3001"
    echo "• Prometheus: http://localhost:9090"
    echo "• Nginx: http://localhost:80"
fi

echo ""
echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo "• View logs: docker-compose -f $COMPOSE_FILE logs -f [service]"
echo "• Stop services: docker-compose -f $COMPOSE_FILE down"
echo "• Restart service: docker-compose -f $COMPOSE_FILE restart [service]"
echo "• Scale service: docker-compose -f $COMPOSE_FILE up -d --scale [service]=N"
echo ""

# Test project creation
echo -e "${BLUE}🧪 Testing project creation...${NC}"
if curl -f -s -X POST "http://localhost:8000/projects" \
    -H "Content-Type: application/json" \
    -d '{"prompt": "Create a simple hello world app", "project_name": "HelloWorld", "project_type": "fullstack"}' > /dev/null; then
    print_status "Project creation test passed"
else
    print_warning "Project creation test failed - this is normal if components are still starting up"
fi

echo ""
echo -e "${GREEN}✨ Aetherforge is ready to create autonomous software projects!${NC}"
