#!/usr/bin/env python3
"""
Complete System Test for Aetherforge
Tests all major components working together
"""

import os
import sys
import asyncio
import tempfile
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_complete_system():
    """Test the complete Aetherforge system"""
    
    print("🔮 Testing Complete Aetherforge System")
    print("=" * 60)
    
    test_results = {
        "orchestrator": False,
        "pheromone_system": False,
        "workflow_engine": False,
        "project_generator": False,
        "component_adapters": False,
        "documentation": False
    }
    
    # Test 1: Orchestrator Core
    print("\n1. Testing Orchestrator Core...")
    try:
        from orchestrator import app, get_default_workflow, get_default_agent_team
        
        # Test workflow selection
        workflow = get_default_workflow("fullstack")
        print(f"   ✅ Default workflow for fullstack: {workflow}")
        
        # Test agent team generation
        agent_team = get_default_agent_team(workflow)
        print(f"   ✅ Agent team generated: {len(agent_team.get('agents', []))} agents")
        
        test_results["orchestrator"] = True
        
    except Exception as e:
        print(f"   ❌ Orchestrator test failed: {e}")
    
    # Test 2: Pheromone System
    print("\n2. Testing Pheromone System...")
    try:
        from pheromone_system import get_pheromone_system
        
        pheromone_system = get_pheromone_system()
        
        # Test pheromone dropping
        pheromone_id = await pheromone_system.drop_pheromone(
            "test_signal", 
            {"test": "data"}, 
            "test_project"
        )
        print(f"   ✅ Pheromone dropped: {pheromone_id}")
        
        # Test statistics
        stats = pheromone_system.get_statistics()
        print(f"   ✅ Statistics retrieved: {stats.get('total_messages', 0)} messages")
        
        test_results["pheromone_system"] = True
        
    except Exception as e:
        print(f"   ❌ Pheromone system test failed: {e}")
    
    # Test 3: Workflow Engine
    print("\n3. Testing Workflow Engine...")
    try:
        from workflow_engine import get_workflow_engine
        
        workflow_engine = get_workflow_engine()
        workflows = workflow_engine.list_workflows()
        
        print(f"   ✅ Workflow engine loaded: {len(workflows)} workflows")
        
        if workflows:
            test_workflow = workflows[0]
            print(f"   ✅ Sample workflow: {test_workflow.name}")
            print(f"      Phases: {len(test_workflow.phases)}")
            print(f"      Duration: {test_workflow.total_duration_minutes} minutes")
        
        test_results["workflow_engine"] = True
        
    except Exception as e:
        print(f"   ❌ Workflow engine test failed: {e}")
    
    # Test 4: Project Generator
    print("\n4. Testing Project Generator...")
    try:
        from project_generator_standalone import ProjectGenerator
        
        generator = ProjectGenerator()
        
        # Test with temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await generator.generate_project(
                prompt="Create a simple test application",
                project_name="TestApp",
                project_type="frontend",
                project_path=str(Path(temp_dir) / "test_app")
            )
            
            print(f"   ✅ Project generated: {result.get('success', False)}")
            print(f"      Files created: {len(result.get('files_created', []))}")
            print(f"      Phases completed: {result.get('phases_completed', 0)}")
        
        test_results["project_generator"] = True
        
    except Exception as e:
        print(f"   ❌ Project generator test failed: {e}")
    
    # Test 5: Component Adapters
    print("\n5. Testing Component Adapters...")
    try:
        from component_adapters_real import ComponentManager
        
        async with ComponentManager() as cm:
            health_results = await cm.health_check_all()
            
            print(f"   ✅ Component health checks completed")
            for component, status in health_results.items():
                status_icon = "✅" if status.get("status") == "healthy" else "⚠️"
                print(f"      {status_icon} {component}: {status.get('status', 'unknown')}")
        
        test_results["component_adapters"] = True
        
    except Exception as e:
        print(f"   ❌ Component adapters test failed: {e}")
    
    # Test 6: Documentation
    print("\n6. Testing Documentation...")
    try:
        docs_files = [
            "AETHERFORGE_DOCUMENTATION.md",
            "INSTALLATION_GUIDE.md", 
            "USER_GUIDE.md",
            "API_REFERENCE.md",
            "README.md"
        ]
        
        docs_found = 0
        for doc_file in docs_files:
            if os.path.exists(doc_file):
                docs_found += 1
                file_size = os.path.getsize(doc_file)
                print(f"   ✅ {doc_file}: {file_size:,} bytes")
            else:
                print(f"   ❌ {doc_file}: Not found")
        
        if docs_found >= 4:  # At least 4 out of 5 docs should exist
            test_results["documentation"] = True
        
    except Exception as e:
        print(f"   ❌ Documentation test failed: {e}")
    
    # Test 7: Integration Test
    print("\n7. Testing System Integration...")
    try:
        # Test that components can work together
        from orchestrator import get_default_workflow, get_default_agent_team
        from workflow_engine import get_workflow_engine
        from pheromone_system import get_pheromone_system
        
        # Simulate a project creation workflow
        project_type = "fullstack"
        workflow_id = get_default_workflow(project_type)
        
        workflow_engine = get_workflow_engine()
        workflow = workflow_engine.get_workflow(workflow_id)
        
        agent_team = get_default_agent_team(workflow_id)
        
        pheromone_system = get_pheromone_system()
        
        print(f"   ✅ Integration test successful:")
        print(f"      Project type: {project_type}")
        print(f"      Workflow: {workflow.name if workflow else workflow_id}")
        print(f"      Agent team: {len(agent_team.get('agents', []))} agents")
        print(f"      Pheromone system: Active")
        
    except Exception as e:
        print(f"   ❌ Integration test failed: {e}")
    
    # Test Results Summary
    print(f"\n🎉 System Test Results")
    print("=" * 40)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for component, passed in test_results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {component.replace('_', ' ').title()}")
    
    print(f"\n📊 Overall Results:")
    print(f"   Tests Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! Aetherforge is fully operational!")
        return True
    elif passed_tests >= total_tests * 0.8:  # 80% pass rate
        print(f"\n✅ MOSTLY WORKING! Aetherforge is operational with minor issues.")
        return True
    else:
        print(f"\n⚠️  PARTIAL FUNCTIONALITY. Some components need attention.")
        return False

def main():
    """Main test function"""
    
    print("🔍 Aetherforge Complete System Test")
    print("This test verifies all major components are working together")
    print()
    
    # Check prerequisites
    required_files = [
        "src/orchestrator.py",
        "src/pheromone_system.py", 
        "src/workflow_engine.py",
        "src/project_generator_standalone.py",
        "src/component_adapters_real.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return 1
    
    print("✅ All required files found")
    
    # Run the complete system test
    try:
        success = asyncio.run(test_complete_system())
        if success:
            print("\n🚀 Aetherforge is ready for autonomous software creation!")
            return 0
        else:
            print("\n🔧 Some components need attention before full operation.")
            return 1
    except Exception as e:
        print(f"\n❌ System test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
