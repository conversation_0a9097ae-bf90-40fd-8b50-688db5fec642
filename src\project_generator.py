"""
Comprehensive Project Generation Pipeline for Aetherforge
Handles complete project creation with real code generation
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
import os

from .component_adapters import ComponentManager
from .agent_executors import create_agent_executor
from .pheromone_bus import get_pheromone_bus

logger = logging.getLogger(__name__)

class ProjectGenerationPipeline:
    """Complete project generation pipeline"""
    
    def __init__(self):
        self.component_manager = None
        self.pheromone_bus = get_pheromone_bus()
    
    async def generate_project(self, prompt: str, project_name: str, project_type: str, 
                             project_path: str, workflow: str = None) -> Dict[str, Any]:
        """Generate a complete project using the full pipeline"""
        
        project_id = project_name.replace(" ", "_").lower()
        
        try:
            # Initialize component manager
            async with ComponentManager() as cm:
                self.component_manager = cm
                
                # Start pheromone bus cleanup task
                await self.pheromone_bus.start_cleanup_task()
                
                # Phase 1: Project Initialization
                await self._drop_pheromone("project_generation_started", {
                    "project_id": project_id,
                    "prompt": prompt,
                    "project_type": project_type,
                    "workflow": workflow
                }, project_id)
                
                init_result = await self._initialize_project(project_path, project_id, prompt)
                
                # Phase 2: Requirements Analysis
                analysis_result = await self._execute_requirements_analysis(
                    prompt, project_path, project_id, project_type
                )
                
                # Phase 3: Architecture Design
                architecture_result = await self._execute_architecture_design(
                    prompt, project_path, project_id, project_type, analysis_result
                )
                
                # Phase 4: Code Generation
                development_result = await self._execute_development(
                    prompt, project_path, project_id, project_type, architecture_result
                )
                
                # Phase 5: Quality Assurance
                qa_result = await self._execute_quality_assurance(
                    prompt, project_path, project_id, project_type, development_result
                )
                
                # Phase 6: Project Finalization
                finalization_result = await self._finalize_project(
                    project_path, project_id, {
                        "analysis": analysis_result,
                        "architecture": architecture_result,
                        "development": development_result,
                        "qa": qa_result
                    }
                )
                
                await self._drop_pheromone("project_generation_completed", {
                    "project_id": project_id,
                    "project_path": project_path,
                    "total_files": finalization_result.get("total_files", 0),
                    "completion_time": datetime.now().isoformat()
                }, project_id)
                
                return {
                    "success": True,
                    "project_id": project_id,
                    "project_path": project_path,
                    "phases_completed": 6,
                    "results": {
                        "initialization": init_result,
                        "analysis": analysis_result,
                        "architecture": architecture_result,
                        "development": development_result,
                        "qa": qa_result,
                        "finalization": finalization_result
                    }
                }
                
        except Exception as e:
            await self._drop_pheromone("project_generation_failed", {
                "project_id": project_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }, project_id)
            
            logger.error(f"Project generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_id": project_id
            }
    
    async def _initialize_project(self, project_path: str, project_id: str, prompt: str) -> Dict[str, Any]:
        """Initialize project structure and metadata"""
        
        await self._drop_pheromone("phase_started", {"phase": "initialization"}, project_id)
        
        project_dir = Path(project_path)
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # Create basic directory structure
        directories = [
            "src", "docs", "tests", "config", "scripts", 
            "assets", "public", "server", "database"
        ]
        
        for dir_name in directories:
            (project_dir / dir_name).mkdir(exist_ok=True)
        
        # Create project metadata
        metadata = {
            "project_id": project_id,
            "name": project_dir.name,
            "description": prompt,
            "created_at": datetime.now().isoformat(),
            "aetherforge_version": "1.0.0",
            "status": "generating",
            "phases": {
                "initialization": "completed",
                "analysis": "pending",
                "architecture": "pending",
                "development": "pending",
                "qa": "pending",
                "finalization": "pending"
            }
        }
        
        metadata_file = project_dir / ".aetherforge.json"
        metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')
        
        # Create initial README
        readme_content = f"""# {project_dir.name}

{prompt}

## Generated by Aetherforge

This project was autonomously generated by Aetherforge AI agents.

**Project ID**: {project_id}  
**Created**: {datetime.now().isoformat()}  
**Status**: Generating...

## Project Structure

- `src/` - Source code
- `docs/` - Documentation
- `tests/` - Test files
- `config/` - Configuration files
- `scripts/` - Build and deployment scripts
- `assets/` - Static assets
- `public/` - Public web assets
- `server/` - Backend server code
- `database/` - Database schemas and migrations

## Development Status

🔄 **In Progress** - AI agents are currently developing this project.

Check the `.aetherforge.json` file for detailed progress information.
"""
        
        readme_file = project_dir / "README.md"
        readme_file.write_text(readme_content, encoding='utf-8')
        
        await self._drop_pheromone("phase_completed", {
            "phase": "initialization",
            "directories_created": len(directories),
            "files_created": ["README.md", ".aetherforge.json"]
        }, project_id)
        
        return {
            "success": True,
            "directories_created": directories,
            "files_created": ["README.md", ".aetherforge.json"],
            "metadata": metadata
        }
    
    async def _execute_requirements_analysis(self, prompt: str, project_path: str, 
                                           project_id: str, project_type: str) -> Dict[str, Any]:
        """Execute requirements analysis phase"""
        
        await self._drop_pheromone("phase_started", {"phase": "requirements_analysis"}, project_id)
        
        try:
            # Create analyst executor
            analyst = create_agent_executor("analyst")
            
            # Execute analysis
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "requirements_analysis"
            }
            
            result = await analyst.execute(context)
            
            # Update project metadata
            await self._update_project_metadata(project_path, "analysis", "completed")
            
            await self._drop_pheromone("phase_completed", {
                "phase": "requirements_analysis",
                "success": result.get("success", False),
                "outputs": result.get("outputs", [])
            }, project_id)
            
            return result
            
        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "requirements_analysis",
                "error": str(e)
            }, project_id)
            raise e
    
    async def _execute_architecture_design(self, prompt: str, project_path: str, 
                                         project_id: str, project_type: str, 
                                         analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute architecture design phase"""
        
        await self._drop_pheromone("phase_started", {"phase": "architecture_design"}, project_id)
        
        try:
            # Create architect executor
            architect = create_agent_executor("architect")
            
            # Execute architecture design
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "architecture_design",
                "analysis_result": analysis_result
            }
            
            result = await architect.execute(context)
            
            # Update project metadata
            await self._update_project_metadata(project_path, "architecture", "completed")
            
            await self._drop_pheromone("phase_completed", {
                "phase": "architecture_design",
                "success": result.get("success", False),
                "outputs": result.get("outputs", [])
            }, project_id)
            
            return result
            
        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "architecture_design",
                "error": str(e)
            }, project_id)
            raise e
    
    async def _execute_development(self, prompt: str, project_path: str, 
                                 project_id: str, project_type: str, 
                                 architecture_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute development phase"""
        
        await self._drop_pheromone("phase_started", {"phase": "development"}, project_id)
        
        try:
            # Create developer executor
            developer = create_agent_executor("developer")
            
            # Execute development
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "development",
                "architecture_result": architecture_result
            }
            
            result = await developer.execute(context)
            
            # Update project metadata
            await self._update_project_metadata(project_path, "development", "completed")
            
            await self._drop_pheromone("phase_completed", {
                "phase": "development",
                "success": result.get("success", False),
                "outputs": result.get("outputs", []),
                "files_created": result.get("files_created", 0)
            }, project_id)
            
            return result
            
        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "development",
                "error": str(e)
            }, project_id)
            raise e

    async def _execute_quality_assurance(self, prompt: str, project_path: str,
                                        project_id: str, project_type: str,
                                        development_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute quality assurance phase"""

        await self._drop_pheromone("phase_started", {"phase": "quality_assurance"}, project_id)

        try:
            # Create QA executor
            qa = create_agent_executor("qa")

            # Execute QA
            context = {
                "prompt": prompt,
                "project_path": project_path,
                "project_id": project_id,
                "project_type": project_type,
                "phase": "quality_assurance",
                "development_result": development_result
            }

            result = await qa.execute(context)

            # Update project metadata
            await self._update_project_metadata(project_path, "qa", "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "quality_assurance",
                "success": result.get("success", False),
                "outputs": result.get("outputs", [])
            }, project_id)

            return result

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "quality_assurance",
                "error": str(e)
            }, project_id)
            raise e

    async def _finalize_project(self, project_path: str, project_id: str,
                              phase_results: Dict[str, Any]) -> Dict[str, Any]:
        """Finalize the project with summary and final documentation"""

        await self._drop_pheromone("phase_started", {"phase": "finalization"}, project_id)

        try:
            project_dir = Path(project_path)

            # Count total files created
            total_files = len(list(project_dir.rglob("*")))

            # Create final project summary
            summary = {
                "project_id": project_id,
                "completion_time": datetime.now().isoformat(),
                "total_files": total_files,
                "phases_completed": len(phase_results),
                "phase_results": {
                    phase: {
                        "success": result.get("success", False),
                        "outputs": len(result.get("outputs", [])),
                        "summary": result.get("summary", "")
                    }
                    for phase, result in phase_results.items()
                }
            }

            # Save project summary
            summary_file = project_dir / "docs" / "project_summary.json"
            summary_file.write_text(json.dumps(summary, indent=2), encoding='utf-8')

            # Update project metadata to completed
            await self._update_project_metadata(project_path, "finalization", "completed")
            await self._update_project_status(project_path, "completed")

            await self._drop_pheromone("phase_completed", {
                "phase": "finalization",
                "total_files": total_files,
                "project_completed": True
            }, project_id)

            return {
                "success": True,
                "total_files": total_files,
                "summary": summary,
                "files_created": ["docs/project_summary.json"]
            }

        except Exception as e:
            await self._drop_pheromone("phase_failed", {
                "phase": "finalization",
                "error": str(e)
            }, project_id)
            raise e

    async def _update_project_metadata(self, project_path: str, phase: str, status: str):
        """Update project metadata with phase completion"""

        metadata_file = Path(project_path) / ".aetherforge.json"

        if metadata_file.exists():
            try:
                metadata = json.loads(metadata_file.read_text(encoding='utf-8'))
                metadata["phases"][phase] = status
                metadata["last_updated"] = datetime.now().isoformat()
                metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')
            except Exception as e:
                logger.warning(f"Failed to update metadata: {e}")

    async def _update_project_status(self, project_path: str, status: str):
        """Update overall project status"""

        metadata_file = Path(project_path) / ".aetherforge.json"

        if metadata_file.exists():
            try:
                metadata = json.loads(metadata_file.read_text(encoding='utf-8'))
                metadata["status"] = status
                metadata["completed_at"] = datetime.now().isoformat()
                metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')
            except Exception as e:
                logger.warning(f"Failed to update project status: {e}")

    async def _drop_pheromone(self, pheromone_type: str, data: Dict[str, Any], project_id: str):
        """Drop a pheromone using the enhanced bus"""
        try:
            await self.pheromone_bus.drop_pheromone(pheromone_type, data, project_id)
        except Exception as e:
            logger.warning(f"Failed to drop pheromone: {e}")


# Factory function for easy access
async def generate_project(prompt: str, project_name: str, project_type: str,
                          project_path: str, workflow: str = None) -> Dict[str, Any]:
    """Generate a complete project using the pipeline"""

    pipeline = ProjectGenerationPipeline()
    return await pipeline.generate_project(prompt, project_name, project_type, project_path, workflow)
