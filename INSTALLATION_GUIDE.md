# Aetherforge Installation Guide

## 🎯 Overview

This guide will walk you through installing and setting up Aetherforge, the autonomous AI software creation system. Follow these steps to get Aetherforge running on your system.

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Internet**: Stable broadband connection for AI API calls

### Software Prerequisites
- **Python**: 3.8 or higher
- **Node.js**: 18.0 or higher
- **VS Code**: Latest version
- **Git**: For cloning repositories

### Optional Components
- **Docker**: For running external components in containers
- **Docker Compose**: For multi-container setups

## 🚀 Quick Installation

### Step 1: Clone the Repository

```bash
git clone https://github.com/your-org/aetherforge.git
cd aetherforge
```

### Step 2: Install Python Dependencies

```bash
# Create virtual environment (recommended)
python -m venv aetherforge-env

# Activate virtual environment
# On Windows:
aetherforge-env\Scripts\activate
# On macOS/Linux:
source aetherforge-env/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Step 3: Install VS Code Extension

```bash
# Install from VSIX file
code --install-extension aetherforge-1.0.0.vsix

# Or install from marketplace (if published)
code --install-extension aetherforge
```

### Step 4: Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env  # or use your preferred editor
```

### Step 5: Start Aetherforge

```bash
# Start the orchestrator
python src/orchestrator.py

# Open VS Code and activate Aetherforge
code .
# Press Ctrl+Shift+P and run "Aetherforge: Start"
```

## 🔧 Detailed Installation

### Python Environment Setup

#### Using Conda (Recommended for Data Science Users)
```bash
# Create conda environment
conda create -n aetherforge python=3.9
conda activate aetherforge

# Install dependencies
pip install -r requirements.txt
```

#### Using pyenv (Recommended for Multiple Python Versions)
```bash
# Install Python 3.9
pyenv install 3.9.16
pyenv local 3.9.16

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Node.js Setup

#### Using Node Version Manager (nvm)
```bash
# Install Node.js 18
nvm install 18
nvm use 18

# Verify installation
node --version
npm --version
```

#### Direct Installation
- Download from [nodejs.org](https://nodejs.org/)
- Install the LTS version (18.x or higher)

### VS Code Extension Development Setup

If you want to modify the extension:

```bash
# Navigate to extension directory
cd vscode-extension

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Package extension
npm run package
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# Required: OpenAI API Key
OPENAI_API_KEY=sk-your-openai-api-key-here

# Optional: External Component URLs
ARCHON_URL=http://localhost:8100
PHEROMIND_URL=http://localhost:8502
MCP_URL=http://localhost:8051
BMAD_URL=http://localhost:8503

# Optional: Paths and Settings
PROJECTS_DIR=./projects
PHEROMONE_FILE=./pheromones.json
HOST=0.0.0.0
PORT=8000

# Optional: Debug Settings
AETHERFORGE_DEBUG=false
LOG_LEVEL=INFO
```

### VS Code Settings

Add to your VS Code `settings.json`:

```json
{
  "aetherforge.orchestratorUrl": "http://localhost:8000",
  "aetherforge.projectsPath": "./projects",
  "aetherforge.autoRefresh": true,
  "aetherforge.enableNotifications": true,
  "aetherforge.debugMode": false
}
```

### Project Directory Structure

Aetherforge will create this structure:

```
aetherforge/
├── src/                    # Core source code
├── projects/              # Generated projects
├── components/            # External components
├── logs/                  # System logs
├── .env                   # Environment configuration
├── requirements.txt       # Python dependencies
└── README.md             # Project documentation
```

## 🐳 Docker Installation (Optional)

### Using Docker Compose

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f orchestrator
```

### Manual Docker Setup

```bash
# Build orchestrator image
docker build -t aetherforge-orchestrator .

# Run orchestrator container
docker run -d \
  --name aetherforge \
  -p 8000:8000 \
  -v $(pwd)/projects:/app/projects \
  -e OPENAI_API_KEY=your-key-here \
  aetherforge-orchestrator
```

## 🔌 External Components Setup

### Archon (Agent Team Generation)

```bash
# Clone Archon repository
git clone https://github.com/archon-ai/archon.git
cd archon

# Install dependencies
pip install -r requirements.txt

# Start Archon service
python app.py --port 8100
```

### Pheromind (Agent Coordination)

```bash
# Clone Pheromind repository
git clone https://github.com/pheromind/pheromind.git
cd pheromind

# Install dependencies
npm install

# Start Pheromind service
npm start
```

### MCP-Crawl4AI-RAG (Web Research)

```bash
# Clone MCP repository
git clone https://github.com/mcp-crawl4ai/mcp-crawl4ai-rag.git
cd mcp-crawl4ai-rag

# Install dependencies
pip install -r requirements.txt

# Start MCP service
python server.py --port 8051
```

## ✅ Verification

### Test Installation

```bash
# Test Python environment
python -c "import fastapi, openai, structlog; print('Python dependencies OK')"

# Test orchestrator
curl http://localhost:8000/health

# Test VS Code extension
code --list-extensions | grep aetherforge
```

### Create Test Project

1. Open VS Code
2. Press `Ctrl+Shift+P`
3. Run "Aetherforge: Start"
4. Enter test prompt: "Create a simple hello world web application"
5. Click "Create Project"
6. Verify project is generated in `./projects/`

## 🐛 Troubleshooting

### Common Issues

#### Python Import Errors
```bash
# Reinstall dependencies
pip install --force-reinstall -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"
```

#### Port Already in Use
```bash
# Find process using port 8000
lsof -i :8000  # On macOS/Linux
netstat -ano | findstr :8000  # On Windows

# Kill process or change port in .env
```

#### VS Code Extension Not Loading
```bash
# Reload VS Code window
Ctrl+Shift+P -> "Developer: Reload Window"

# Check extension logs
Ctrl+Shift+P -> "Developer: Show Logs" -> "Extension Host"
```

#### OpenAI API Errors
- Verify API key is correct
- Check API quota and billing
- Test with simple API call:
```python
import openai
client = openai.OpenAI(api_key="your-key")
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello"}]
)
print(response.choices[0].message.content)
```

### Debug Mode

Enable detailed logging:

```bash
export AETHERFORGE_DEBUG=true
export LOG_LEVEL=DEBUG
python src/orchestrator.py
```

### Log Files

Check these locations for logs:
- `./logs/orchestrator.log` - Main application logs
- `./logs/pheromone.log` - Agent communication logs
- VS Code Developer Console - Extension logs

## 🔄 Updates

### Updating Aetherforge

```bash
# Pull latest changes
git pull origin main

# Update Python dependencies
pip install -r requirements.txt --upgrade

# Update VS Code extension
code --install-extension aetherforge-latest.vsix

# Restart services
python src/orchestrator.py
```

### Version Management

```bash
# Check current version
python -c "from src.orchestrator import __version__; print(__version__)"

# List available versions
git tag -l
```

## 📞 Support

### Getting Help

1. **Documentation**: Check `AETHERFORGE_DOCUMENTATION.md`
2. **Issues**: Create GitHub issue with logs and system info
3. **Community**: Join Discord/Slack community
4. **Email**: <EMAIL>

### System Information

When reporting issues, include:

```bash
# System info
python --version
node --version
code --version
docker --version

# Aetherforge info
python -c "from src.orchestrator import get_system_info; print(get_system_info())"
```

---

**Installation complete!** 🎉 You're ready to create autonomous software projects with Aetherforge.
