#!/usr/bin/env python3
"""
Test Standalone Project Generator
Tests the standalone project generation functionality
"""

import os
import sys
import asyncio
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_standalone_generator():
    """Test the standalone project generator"""
    
    print("🔮 Testing Aetherforge Standalone Project Generator")
    print("=" * 60)
    
    # Create temporary directory for test
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = Path(temp_dir)
        
        print(f"📁 Test output directory: {output_dir}")
        
        try:
            from project_generator_standalone import generate_project_standalone
            
            # Test project generation
            print("\n🚀 Generating test project...")
            
            result = await generate_project_standalone(
                prompt="Create a todo list application with user authentication and real-time updates",
                project_name="TestTodoApp",
                project_type="fullstack",
                output_dir=str(output_dir)
            )
            
            print(f"\n📊 Generation Result:")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Project ID: {result.get('project_id', 'N/A')}")
            print(f"   Files Created: {len(result.get('files_created', []))}")
            print(f"   Phases Completed: {result.get('phases_completed', 0)}/6")
            
            if result.get('error'):
                print(f"   Error: {result['error']}")
            
            # Check if project directory was created
            project_path = Path(result.get('project_path', ''))
            if project_path.exists():
                print(f"\n✅ Project directory created: {project_path}")
                
                # List all created files
                all_files = []
                for root, dirs, files in os.walk(project_path):
                    for file in files:
                        rel_path = os.path.relpath(os.path.join(root, file), project_path)
                        all_files.append(rel_path)
                
                print(f"\n📄 Files created ({len(all_files)} total):")
                for file_path in sorted(all_files):
                    file_full_path = project_path / file_path
                    file_size = file_full_path.stat().st_size if file_full_path.exists() else 0
                    print(f"   ✅ {file_path} ({file_size} bytes)")
                
                # Check key files
                key_files = [
                    "README.md",
                    "package.json",
                    ".aetherforge.json",
                    "src/App.tsx",
                    "server/index.js",
                    "Dockerfile",
                    "docker-compose.yml"
                ]
                
                print(f"\n🔍 Key files check:")
                for key_file in key_files:
                    file_path = project_path / key_file
                    if file_path.exists():
                        print(f"   ✅ {key_file}")
                        
                        # Show preview of some files
                        if key_file in ["README.md", "package.json", ".aetherforge.json"]:
                            try:
                                content = file_path.read_text(encoding='utf-8')
                                preview = content[:200] + "..." if len(content) > 200 else content
                                print(f"      Preview: {preview}")
                            except Exception as e:
                                print(f"      Error reading file: {e}")
                    else:
                        print(f"   ❌ {key_file} missing")
                
                # Check directory structure
                expected_dirs = ["src", "server", "docs", "tests", "config"]
                print(f"\n📁 Directory structure check:")
                for dir_name in expected_dirs:
                    dir_path = project_path / dir_name
                    if dir_path.exists() and dir_path.is_dir():
                        file_count = len(list(dir_path.rglob("*")))
                        print(f"   ✅ {dir_name}/ ({file_count} items)")
                    else:
                        print(f"   ❌ {dir_name}/ missing")
                
                # Validate package.json
                package_json_path = project_path / "package.json"
                if package_json_path.exists():
                    try:
                        import json
                        package_data = json.loads(package_json_path.read_text())
                        print(f"\n📦 Package.json validation:")
                        print(f"   Name: {package_data.get('name', 'N/A')}")
                        print(f"   Version: {package_data.get('version', 'N/A')}")
                        print(f"   Dependencies: {len(package_data.get('dependencies', {}))}")
                        print(f"   Dev Dependencies: {len(package_data.get('devDependencies', {}))}")
                        print(f"   Scripts: {len(package_data.get('scripts', {}))}")
                    except Exception as e:
                        print(f"   ❌ Error validating package.json: {e}")
                
                # Validate metadata
                metadata_path = project_path / ".aetherforge.json"
                if metadata_path.exists():
                    try:
                        import json
                        metadata = json.loads(metadata_path.read_text())
                        print(f"\n🔮 Aetherforge metadata:")
                        print(f"   Project ID: {metadata.get('project_id', 'N/A')}")
                        print(f"   Status: {metadata.get('status', 'N/A')}")
                        print(f"   Generator: {metadata.get('generator', 'N/A')}")
                        print(f"   Files Generated: {len(metadata.get('files_generated', []))}")
                        print(f"   Phases: {metadata.get('phases_completed', 0)}/{metadata.get('total_phases', 6)}")
                    except Exception as e:
                        print(f"   ❌ Error validating metadata: {e}")
                
                print(f"\n🎉 Test completed successfully!")
                print(f"📊 Summary: {len(all_files)} files created in {len(expected_dirs)} directories")
                
                return True
            else:
                print(f"❌ Project directory not created")
                return False
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main test function"""
    
    print("🔍 Checking prerequisites...")
    
    # Check if src directory exists
    if os.path.exists("src"):
        print("✅ Source directory found")
    else:
        print("❌ Source directory not found")
        return 1
    
    # Check if project generator exists
    if os.path.exists("src/project_generator_standalone.py"):
        print("✅ Standalone project generator found")
    else:
        print("❌ Standalone project generator not found")
        return 1
    
    # Run the test
    try:
        success = asyncio.run(test_standalone_generator())
        if success:
            print("\n🎉 All tests passed!")
            return 0
        else:
            print("\n❌ Some tests failed")
            return 1
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
