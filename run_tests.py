#!/usr/bin/env python3
"""
Aetherforge Test Runner

This script provides a comprehensive test runner for the Aetherforge system.
It can run different types of tests and generate reports.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path


def run_command(command, cwd=None, timeout=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)


def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    dependencies = [
        ("python", "python --version"),
        ("pytest", "pytest --version"),
        ("pip", "pip --version")
    ]
    
    missing = []
    for name, command in dependencies:
        success, stdout, stderr = run_command(command)
        if success:
            version = stdout.strip().split('\n')[0]
            print(f"  ✓ {name}: {version}")
        else:
            print(f"  ✗ {name}: Not found")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Please install missing dependencies and try again.")
        return False
    
    print("✅ All dependencies found")
    return True


def install_test_dependencies():
    """Install test-specific dependencies"""
    print("📦 Installing test dependencies...")
    
    test_requirements = [
        "pytest>=7.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-html>=3.1.0",
        "pytest-xdist>=3.0.0",
        "requests>=2.28.0",
        "httpx>=0.24.0"
    ]
    
    for requirement in test_requirements:
        print(f"  Installing {requirement}...")
        success, stdout, stderr = run_command(f"pip install {requirement}")
        if not success:
            print(f"  ⚠️  Failed to install {requirement}: {stderr}")
        else:
            print(f"  ✓ Installed {requirement}")
    
    print("✅ Test dependencies installed")


def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests"""
    print("🧪 Running unit tests...")
    
    command = "pytest tests/test_orchestrator.py tests/test_vscode_extension.py"
    
    if verbose:
        command += " -v"
    
    if coverage:
        command += " --cov=src --cov-report=html --cov-report=term"
    
    success, stdout, stderr = run_command(command, timeout=300)
    
    if success:
        print("✅ Unit tests passed")
        if verbose:
            print(stdout)
    else:
        print("❌ Unit tests failed")
        print(stderr)
        if verbose:
            print(stdout)
    
    return success


def run_integration_tests(verbose=False):
    """Run integration tests"""
    print("🔗 Running integration tests...")
    
    command = "pytest tests/test_integration.py --integration"
    
    if verbose:
        command += " -v -s"
    
    success, stdout, stderr = run_command(command, timeout=600)
    
    if success:
        print("✅ Integration tests passed")
        if verbose:
            print(stdout)
    else:
        print("❌ Integration tests failed")
        print(stderr)
        if verbose:
            print(stdout)
    
    return success


def run_performance_tests(verbose=False):
    """Run performance tests"""
    print("⚡ Running performance tests...")
    
    command = "pytest tests/test_integration.py::TestPerformance --integration"
    
    if verbose:
        command += " -v -s"
    
    success, stdout, stderr = run_command(command, timeout=300)
    
    if success:
        print("✅ Performance tests passed")
        if verbose:
            print(stdout)
    else:
        print("❌ Performance tests failed")
        print(stderr)
        if verbose:
            print(stdout)
    
    return success


def run_linting():
    """Run code linting"""
    print("🔍 Running code linting...")
    
    # Check if flake8 is available
    success, _, _ = run_command("flake8 --version")
    if not success:
        print("  Installing flake8...")
        run_command("pip install flake8")
    
    # Run flake8 on source code
    command = "flake8 src/ --max-line-length=100 --ignore=E501,W503"
    success, stdout, stderr = run_command(command)
    
    if success:
        print("✅ Linting passed")
    else:
        print("❌ Linting failed")
        print(stderr)
    
    return success


def run_type_checking():
    """Run type checking with mypy"""
    print("🔍 Running type checking...")
    
    # Check if mypy is available
    success, _, _ = run_command("mypy --version")
    if not success:
        print("  Installing mypy...")
        run_command("pip install mypy")
    
    # Run mypy on source code
    command = "mypy src/ --ignore-missing-imports"
    success, stdout, stderr = run_command(command)
    
    if success:
        print("✅ Type checking passed")
    else:
        print("❌ Type checking failed")
        print(stderr)
    
    return success


def generate_test_report():
    """Generate comprehensive test report"""
    print("📊 Generating test report...")
    
    command = (
        "pytest tests/ --integration --html=test_report.html "
        "--cov=src --cov-report=html --cov-report=term "
        "--junitxml=test_results.xml"
    )
    
    success, stdout, stderr = run_command(command, timeout=900)
    
    if success:
        print("✅ Test report generated")
        print("  📄 HTML report: test_report.html")
        print("  📄 Coverage report: htmlcov/index.html")
        print("  📄 JUnit XML: test_results.xml")
    else:
        print("❌ Test report generation failed")
        print(stderr)
    
    return success


def run_docker_tests():
    """Run tests in Docker environment"""
    print("🐳 Running tests in Docker...")
    
    # Check if Docker is available
    success, _, _ = run_command("docker --version")
    if not success:
        print("❌ Docker not available")
        return False
    
    # Build test image
    print("  Building test image...")
    success, stdout, stderr = run_command(
        "docker build -f Dockerfile.test -t aetherforge-test .",
        timeout=300
    )
    
    if not success:
        print(f"❌ Failed to build test image: {stderr}")
        return False
    
    # Run tests in container
    print("  Running tests in container...")
    success, stdout, stderr = run_command(
        "docker run --rm aetherforge-test",
        timeout=600
    )
    
    if success:
        print("✅ Docker tests passed")
        print(stdout)
    else:
        print("❌ Docker tests failed")
        print(stderr)
    
    return success


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="Aetherforge Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    parser.add_argument("--lint", action="store_true", help="Run linting only")
    parser.add_argument("--type-check", action="store_true", help="Run type checking only")
    parser.add_argument("--docker", action="store_true", help="Run tests in Docker")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--report", action="store_true", help="Generate comprehensive test report")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    
    args = parser.parse_args()
    
    print("🔮 Aetherforge Test Runner")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Install test dependencies if requested
    if args.install_deps:
        install_test_dependencies()
        return
    
    # Track test results
    results = {}
    start_time = time.time()
    
    try:
        if args.unit or args.all:
            results['unit'] = run_unit_tests(args.verbose, args.coverage)
        
        if args.integration or args.all:
            results['integration'] = run_integration_tests(args.verbose)
        
        if args.performance or args.all:
            results['performance'] = run_performance_tests(args.verbose)
        
        if args.lint or args.all:
            results['lint'] = run_linting()
        
        if args.type_check or args.all:
            results['type_check'] = run_type_checking()
        
        if args.docker:
            results['docker'] = run_docker_tests()
        
        if args.report:
            results['report'] = generate_test_report()
        
        # If no specific test type was requested, run unit tests
        if not any([args.unit, args.integration, args.performance, args.lint, 
                   args.type_check, args.docker, args.all, args.report]):
            results['unit'] = run_unit_tests(args.verbose, args.coverage)
    
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    
    # Print summary
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print(f"⏱️  Total time: {duration:.2f} seconds")
    
    if results:
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        print(f"✅ Passed: {passed}/{total}")
        
        for test_type, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {test_type}: {status}")
        
        if passed == total:
            print("\n🎉 All tests passed!")
            sys.exit(0)
        else:
            print(f"\n❌ {total - passed} test(s) failed")
            sys.exit(1)
    else:
        print("No tests were run")


if __name__ == "__main__":
    main()
