#!/usr/bin/env python3
"""
Aetherforge Complete System Startup Script
Starts all components and validates the system is ready
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path
from datetime import datetime

def print_banner():
    """Print Aetherforge banner"""
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║      🔮 AETHERFORGE - Autonomous AI Software Creation        ║
    ║                                                               ║
    ║      Transform ideas into complete software projects          ║
    ║      using coordinated AI agents and real-time pheromones    ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_status(message, status="INFO"):
    """Print a status message"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    status_symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅",
        "ERROR": "❌",
        "WARNING": "⚠️",
        "RUNNING": "🔄"
    }
    symbol = status_symbols.get(status, "•")
    print(f"[{timestamp}] {symbol} {message}")

def check_prerequisites():
    """Check system prerequisites"""
    print_status("Checking system prerequisites...", "RUNNING")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print_status("Python 3.8+ required", "ERROR")
        return False
    
    print_status(f"Python {sys.version_info.major}.{sys.version_info.minor} - OK", "SUCCESS")
    
    # Check required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print_status("OPENAI_API_KEY environment variable required", "ERROR")
        print_status("Set it with: export OPENAI_API_KEY=your_api_key", "INFO")
        return False
    
    print_status("OpenAI API key configured", "SUCCESS")
    
    # Check required packages
    required_packages = ["fastapi", "uvicorn", "requests", "openai"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print_status(f"Missing packages: {', '.join(missing_packages)}", "ERROR")
        print_status(f"Install with: pip install {' '.join(missing_packages)}", "INFO")
        return False
    
    print_status("All required packages available", "SUCCESS")
    return True

def setup_environment():
    """Setup Aetherforge environment"""
    print_status("Setting up Aetherforge environment...", "RUNNING")
    
    # Create projects directory
    projects_dir = Path("projects")
    projects_dir.mkdir(exist_ok=True)
    print_status(f"Projects directory: {projects_dir.absolute()}", "SUCCESS")
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    print_status(f"Logs directory: {logs_dir.absolute()}", "SUCCESS")
    
    # Set default environment variables
    os.environ.setdefault("PROJECTS_DIR", str(projects_dir.absolute()))
    os.environ.setdefault("LOG_LEVEL", "info")
    os.environ.setdefault("ORCHESTRATOR_URL", "http://localhost:8000")
    
    return True

def start_orchestrator():
    """Start the Aetherforge orchestrator"""
    print_status("Starting Aetherforge orchestrator...", "RUNNING")
    
    try:
        # Start orchestrator process
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "src.orchestrator:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--log-level", "info"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for startup
        print_status("Waiting for orchestrator to start...", "RUNNING")
        
        for attempt in range(30):  # Wait up to 30 seconds
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    health_data = response.json()
                    print_status(f"Orchestrator started successfully: {health_data.get('status')}", "SUCCESS")
                    return process
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        print_status("Orchestrator failed to start within 30 seconds", "ERROR")
        process.terminate()
        return None
        
    except Exception as e:
        print_status(f"Failed to start orchestrator: {e}", "ERROR")
        return None

def validate_system():
    """Validate that all system components are working"""
    print_status("Validating system components...", "RUNNING")
    
    try:
        # Test health endpoint
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print_status("Health check failed", "ERROR")
            return False
        
        print_status("Health check passed", "SUCCESS")
        
        # Test components status
        response = requests.get("http://localhost:8000/components/status", timeout=5)
        if response.status_code == 200:
            print_status("Components status endpoint working", "SUCCESS")
        
        # Test pheromone system
        response = requests.get("http://localhost:8000/pheromones/statistics", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print_status("Pheromone system active", "SUCCESS")
        
        # Test API documentation
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print_status("API documentation available at http://localhost:8000/docs", "SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"System validation failed: {e}", "ERROR")
        return False

def show_usage_instructions():
    """Show usage instructions"""
    print("\n" + "=" * 70)
    print("  🚀 AETHERFORGE IS READY!")
    print("=" * 70)
    
    print("\n📋 How to use Aetherforge:")
    print("\n1. 🌐 Web Interface:")
    print("   • Open: http://localhost:8000/docs")
    print("   • Use the interactive API documentation")
    print("   • Create projects via POST /projects endpoint")
    
    print("\n2. 🔧 VS Code Extension:")
    print("   • Install the Aetherforge VS Code extension")
    print("   • Press Ctrl+Shift+P → 'Aetherforge: Create Project'")
    print("   • Follow the guided project creation process")
    
    print("\n3. 📡 API Usage:")
    print("   • Health check: GET http://localhost:8000/health")
    print("   • Create project: POST http://localhost:8000/projects")
    print("   • List projects: GET http://localhost:8000/projects")
    print("   • Pheromone stats: GET http://localhost:8000/pheromones/statistics")
    
    print("\n4. 📁 Project Structure:")
    print("   • Generated projects: ./projects/")
    print("   • Logs: ./logs/")
    print("   • Configuration: ./aetherforge_config.json")
    
    print("\n5. 🔮 Example Project Creation:")
    print("""   curl -X POST http://localhost:8000/projects \\
     -H "Content-Type: application/json" \\
     -d '{
       "prompt": "Create a todo app with user authentication",
       "project_name": "MyTodoApp",
       "project_type": "fullstack",
       "workflow": "greenfield-fullstack"
     }'""")
    
    print("\n6. 📊 Monitoring:")
    print("   • Real-time pheromone activity")
    print("   • Project generation progress")
    print("   • Component health status")
    
    print("\n" + "=" * 70)
    print("  Press Ctrl+C to stop Aetherforge")
    print("=" * 70)

def main():
    """Main startup function"""
    print_banner()
    
    print_status("Starting Aetherforge Complete System", "INFO")
    
    # Check prerequisites
    if not check_prerequisites():
        print_status("Prerequisites check failed", "ERROR")
        return 1
    
    # Setup environment
    if not setup_environment():
        print_status("Environment setup failed", "ERROR")
        return 1
    
    # Start orchestrator
    orchestrator_process = start_orchestrator()
    if not orchestrator_process:
        print_status("Failed to start orchestrator", "ERROR")
        return 1
    
    # Validate system
    if not validate_system():
        print_status("System validation failed", "ERROR")
        orchestrator_process.terminate()
        return 1
    
    # Show usage instructions
    show_usage_instructions()
    
    try:
        # Keep the system running
        while True:
            time.sleep(1)
            
            # Check if orchestrator is still running
            if orchestrator_process.poll() is not None:
                print_status("Orchestrator process terminated unexpectedly", "ERROR")
                break
                
    except KeyboardInterrupt:
        print_status("\nShutting down Aetherforge...", "INFO")
        
        # Graceful shutdown
        try:
            orchestrator_process.terminate()
            orchestrator_process.wait(timeout=10)
            print_status("Orchestrator stopped gracefully", "SUCCESS")
        except subprocess.TimeoutExpired:
            print_status("Force killing orchestrator", "WARNING")
            orchestrator_process.kill()
        
        print_status("Aetherforge shutdown complete", "SUCCESS")
        return 0
    
    return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
