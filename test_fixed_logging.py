#!/usr/bin/env python3
"""
Test Fixed Logging System
Tests the logging system with proper cleanup to avoid Windows file locking issues
"""

import os
import sys
import asyncio
import tempfile
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def cleanup_logger(logger):
    """Properly cleanup a logger and its handlers"""
    for handler in logger.handlers[:]:
        handler.close()
        logger.removeHandler(handler)

async def test_logging_with_cleanup():
    """Test logging system with proper cleanup"""
    print("🔍 Testing Logging System with Proper Cleanup...")
    
    try:
        from orchestrator import setup_enhanced_logging, setup_project_logging
        print("   ✅ Logging functions imported successfully")
        
        # Test enhanced logging
        main_logger = setup_enhanced_logging()
        print(f"   ✅ Enhanced logging setup: {main_logger.name} with {len(main_logger.handlers)} handlers")
        
        # Test project logging with manual cleanup
        temp_dir = Path(tempfile.mkdtemp())
        project_path = temp_dir / "test_project"
        project_path.mkdir()
        
        try:
            project_logger = setup_project_logging("test_cleanup", project_path)
            print(f"   ✅ Project logging setup: {project_logger.name}")
            
            # Test logging
            project_logger.info("Test message for cleanup test")
            
            # Check log file
            log_file = project_path / "logs" / "project.log"
            if log_file.exists():
                print(f"   ✅ Log file created: {log_file}")
                print(f"      File size: {log_file.stat().st_size} bytes")
            
            # Manually cleanup the logger
            cleanup_logger(project_logger)
            print("   ✅ Project logger cleaned up")
            
        finally:
            # Cleanup temp directory
            import shutil
            try:
                shutil.rmtree(temp_dir)
                print("   ✅ Temp directory cleaned up successfully")
            except Exception as e:
                print(f"   ⚠️  Temp directory cleanup warning: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Logging test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_generation_with_cleanup():
    """Test agent generation with proper logging cleanup"""
    print("🔍 Testing Agent Generation with Proper Cleanup...")
    
    try:
        from orchestrator import (
            generate_enhanced_agent_team, 
            ProjectRequest, 
            ProjectType, 
            AgentBehavior
        )
        print("   ✅ Agent classes imported successfully")
        
        # Create test request
        test_request = ProjectRequest(
            prompt="Create a comprehensive e-commerce platform with user management, product catalog, shopping cart, and payment processing",
            project_type=ProjectType.FULLSTACK,
            agent_behavior=AgentBehavior.BALANCED,
            enable_parallel_execution=True,
            programming_languages=["Python", "JavaScript"],
            frameworks=["FastAPI", "React"],
            databases=["PostgreSQL"]
        )
        print("   ✅ Test request created successfully")
        
        # Create a simple mock logger instead of file-based logger
        mock_logger = logging.getLogger(f"test_agent_mock")
        mock_logger.setLevel(logging.INFO)
        
        # Add only console handler
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        mock_logger.addHandler(console_handler)
        
        try:
            # Generate agent team
            agent_team = await generate_enhanced_agent_team(
                test_request, 
                "fullstack_workflow", 
                "test_cleanup_456", 
                mock_logger
            )
            
            print(f"   ✅ Agent team generated successfully")
            print(f"      Team ID: {agent_team.get('team_id', 'N/A')}")
            print(f"      Agents: {len(agent_team.get('agents', []))}")
            print(f"      Behavior: {agent_team.get('behavior', 'N/A')}")
            print(f"      Parallel execution: {agent_team.get('parallel_execution', False)}")
            
            # Show agent details
            agents = agent_team.get('agents', [])
            for i, agent in enumerate(agents[:5]):  # Show first 5 agents
                print(f"      Agent {i+1}: {agent.get('role', 'unknown')} - {agent.get('name', 'unnamed')}")
                print(f"         Capabilities: {', '.join(agent.get('capabilities', [])[:3])}")  # First 3 capabilities
            
            # Check for specialized agents based on project type
            roles = [agent.get('role') for agent in agents]
            expected_roles = ['analyst', 'architect', 'developer', 'qa']
            
            for role in expected_roles:
                if role in roles:
                    print(f"   ✅ {role.title()} agent present")
                else:
                    print(f"   ⚠️  {role.title()} agent missing")
            
        finally:
            # Cleanup mock logger
            cleanup_logger(mock_logger)
            print("   ✅ Mock logger cleaned up")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Agent generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_configuration_comprehensive():
    """Test configuration system comprehensively"""
    print("🔍 Testing Configuration System Comprehensively...")
    
    try:
        from orchestrator import (
            OrchestratorConfig, 
            ProjectType, 
            AgentBehavior, 
            Priority,
            AetherforgeError,
            ProjectCreationError
        )
        print("   ✅ All configuration classes imported successfully")
        
        # Test configuration
        config = OrchestratorConfig()
        print(f"   ✅ Configuration created")
        print(f"      Projects dir: {config.projects_dir}")
        print(f"      Logs dir: {config.logs_dir}")
        print(f"      Max concurrent projects: {config.max_concurrent_projects}")
        print(f"      Agent timeout: {config.agent_timeout}s")
        print(f"      Default behavior: {config.default_agent_behavior}")
        
        # Test enums
        project_types = list(ProjectType)
        agent_behaviors = list(AgentBehavior)
        priorities = list(Priority)
        
        print(f"   ✅ ProjectType enum: {len(project_types)} types")
        print(f"      Types: {', '.join([pt.value for pt in project_types[:5]])}...")
        
        print(f"   ✅ AgentBehavior enum: {len(agent_behaviors)} behaviors")
        print(f"      Behaviors: {', '.join([ab.value for ab in agent_behaviors])}")
        
        print(f"   ✅ Priority enum: {len(priorities)} levels")
        print(f"      Priorities: {', '.join([p.value for p in priorities])}")
        
        # Test error classes
        try:
            raise ProjectCreationError(
                "Test error for validation",
                error_code="TEST_VALIDATION_ERROR",
                details={"test_field": "test_value"}
            )
        except ProjectCreationError as e:
            print(f"   ✅ Custom error handling works")
            print(f"      Error code: {e.error_code}")
            print(f"      Error details: {e.details}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function with proper cleanup"""
    print("🔧 Testing Enhanced Orchestrator with Proper Cleanup")
    print("=" * 60)
    
    results = {}
    
    # Test configuration (foundation)
    results["configuration"] = await test_configuration_comprehensive()
    
    # Test logging with cleanup
    results["logging"] = await test_logging_with_cleanup()
    
    # Test agent generation with cleanup
    results["agents"] = await test_agent_generation_with_cleanup()
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {test_name.title()}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\nOverall: {passed_count}/{total_count} tests passed ({(passed_count/total_count)*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 All enhanced features working with proper cleanup!")
        return True
    else:
        print("🔧 Some issues resolved, others may need attention.")
        return passed_count >= total_count * 0.67  # 67% pass rate acceptable

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
