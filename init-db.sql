-- init-db.sql - Database initialization for Aetherforge

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    prompt TEXT NOT NULL,
    project_type VARCHAR(100) DEFAULT 'fullstack',
    workflow VARCHAR(100) DEFAULT 'greenfield-fullstack',
    status VARCHAR(50) DEFAULT 'created',
    progress DECIMAL(5,2) DEFAULT 0.00,
    current_phase VARCHAR(100),
    project_path TEXT,
    trail_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Create agents table
CREATE TABLE IF NOT EXISTS agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    role VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    capabilities JSONB DEFAULT '[]',
    status VARCHAR(50) DEFAULT 'idle',
    current_task TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create pheromones table
CREATE TABLE IF NOT EXISTS pheromones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    signal VARCHAR(255) NOT NULL,
    payload JSONB NOT NULL,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    agent_id VARCHAR(255),
    trail_id VARCHAR(255),
    priority INTEGER DEFAULT 5,
    ttl_hours INTEGER DEFAULT 24,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create project_phases table
CREATE TABLE IF NOT EXISTS project_phases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    phase_name VARCHAR(100) NOT NULL,
    phase_number INTEGER NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    expected_outputs JSONB DEFAULT '[]',
    actual_outputs JSONB DEFAULT '[]',
    agents_assigned JSONB DEFAULT '[]',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create component_status table
CREATE TABLE IF NOT EXISTS component_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    component_name VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(50) NOT NULL,
    url VARCHAR(255),
    version VARCHAR(50),
    health_data JSONB DEFAULT '{}',
    last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_projects_slug ON projects(slug);

CREATE INDEX IF NOT EXISTS idx_agents_project_id ON agents(project_id);
CREATE INDEX IF NOT EXISTS idx_agents_role ON agents(role);
CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status);

CREATE INDEX IF NOT EXISTS idx_pheromones_project_id ON pheromones(project_id);
CREATE INDEX IF NOT EXISTS idx_pheromones_signal ON pheromones(signal);
CREATE INDEX IF NOT EXISTS idx_pheromones_trail_id ON pheromones(trail_id);
CREATE INDEX IF NOT EXISTS idx_pheromones_created_at ON pheromones(created_at);
CREATE INDEX IF NOT EXISTS idx_pheromones_expires_at ON pheromones(expires_at);

CREATE INDEX IF NOT EXISTS idx_project_phases_project_id ON project_phases(project_id);
CREATE INDEX IF NOT EXISTS idx_project_phases_status ON project_phases(status);

CREATE INDEX IF NOT EXISTS idx_component_status_name ON component_status(component_name);
CREATE INDEX IF NOT EXISTS idx_component_status_last_check ON component_status(last_check);

-- Insert initial component status records
INSERT INTO component_status (component_name, status, url, version) VALUES
    ('orchestrator', 'unknown', 'http://orchestrator:8000', '0.1.0'),
    ('archon', 'unknown', 'http://archon:8100', '0.1.0'),
    ('mcp-crawl4ai', 'unknown', 'http://mcp-crawl4ai:8051', '0.1.0'),
    ('pheromind', 'unknown', 'http://pheromind:8502', '0.1.0'),
    ('bmad', 'unknown', 'http://bmad:8503', '0.1.0')
ON CONFLICT (component_name) DO NOTHING;

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to clean up expired pheromones
CREATE OR REPLACE FUNCTION cleanup_expired_pheromones()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM pheromones 
    WHERE expires_at IS NOT NULL AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a view for project statistics
CREATE OR REPLACE VIEW project_statistics AS
SELECT 
    COUNT(*) as total_projects,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_projects,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_projects,
    AVG(progress) as average_progress,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as projects_today,
    COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as projects_this_week
FROM projects;

-- Create a view for pheromone statistics
CREATE OR REPLACE VIEW pheromone_statistics AS
SELECT 
    COUNT(*) as total_pheromones,
    COUNT(DISTINCT signal) as unique_signals,
    COUNT(DISTINCT project_id) as projects_with_pheromones,
    COUNT(CASE WHEN created_at >= CURRENT_DATE THEN 1 END) as pheromones_today,
    signal,
    COUNT(*) as signal_count
FROM pheromones 
WHERE expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP
GROUP BY signal
ORDER BY signal_count DESC;

-- Grant permissions (adjust as needed for your security requirements)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO aetherforge;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO aetherforge;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO aetherforge;
