# 🔮 Aetherforge - Autonomous AI Software Creation System

Aetherforge is a revolutionary autonomous AI system that creates complete software projects from natural language descriptions. It orchestrates multiple specialized AI agents to handle every aspect of software development - from requirements analysis to deployment.

## 🌟 Features

- **Autonomous Project Creation**: Describe your project in natural language and watch it come to life
- **Multi-Agent Architecture**: Specialized AI agents for different development phases
- **Full-Stack Support**: Creates complete applications with frontend, backend, and database
- **Multiple Project Types**: Web apps, mobile apps, APIs, desktop applications, and more
- **Intelligent Orchestration**: Smart coordination between different AI agents
- **Real-time Monitoring**: Track project creation progress with pheromone-based coordination
- **VS Code Integration**: Seamless integration with your favorite IDE
- **Docker Containerization**: Easy deployment and scaling with Docker
- **Comprehensive Monitoring**: Built-in observability with Prometheus and Grafana

## 🏗️ Architecture

Aetherforge consists of several interconnected components:

### Core Components

1. **Orchestrator** (`src/orchestrator.py`) - Central coordination service
2. **Archon** - Agent team generation and management
3. **MCP-Crawl4AI-RAG** - Web research and documentation crawling
4. **Pheromind** - Pheromone-based agent coordination and visualization
5. **BMAD-METHOD** - Development methodology and workflow management

### Agent Types

- **Requirements Analyst** - Analyzes requirements and creates user stories
- **System Architect** - Designs system architecture and selects technologies
- **Full Stack Developer** - Implements frontend and backend code
- **Quality Assurance** - Tests and validates the implementation

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Node.js 16+
- Docker & Docker Compose (recommended)
- VS Code (for the extension)

### Option 1: Docker Deployment (Recommended)

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/aetherforge.git
   cd aetherforge
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys (especially OPENAI_API_KEY)
   ```

3. **Deploy with Docker**
   ```bash
   ./deploy.sh
   ```

4. **Access the system**
   - Orchestrator API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Archon UI: http://localhost:8501
   - Pheromind Visualizer: http://localhost:8502

### Option 2: Local Development

1. **Clone and setup**
   ```bash
   git clone https://github.com/your-org/aetherforge.git
   cd aetherforge
   pip install -r requirements.txt
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the orchestrator**
   ```bash
   uvicorn src.orchestrator:app --host 0.0.0.0 --port 8000
   ```

## 📖 Usage

### Creating a Project

#### Via API
```bash
curl -X POST http://localhost:8000/projects \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a task management app with user authentication, task creation, assignment, and progress tracking",
    "project_name": "TaskMaster",
    "project_type": "fullstack"
  }'
```

#### Via VS Code Extension
1. Open VS Code
2. Run command: "Aetherforge: Create Project"
3. Use the enhanced UI with tabs for:
   - **Create Project**: Describe your project with advanced options
   - **System Status**: Monitor component health
   - **Projects**: View and manage generated projects
   - **Settings**: Configure orchestrator URL and paths

#### Via Management Script
```bash
# Create a test project
./manage.sh create-project

# Check system status
./manage.sh health

# View all projects
./manage.sh status
```

### Project Types

- `fullstack` - Complete web application with frontend and backend
- `frontend` - Frontend-only application (React, Vue, Angular)
- `backend` - Backend API service (Express, FastAPI, Django)
- `mobile` - Mobile application (React Native, Flutter)
- `desktop` - Desktop application (Electron, Tauri)
- `api` - REST API service
- `game` - Game development project

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# API Keys (Required)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Service URLs
ORCHESTRATOR_URL=http://localhost:8000
ARCHON_URL=http://localhost:8100
MCP_URL=http://localhost:8051
PHEROMIND_URL=http://localhost:8502
BMAD_URL=http://localhost:8503

# Database
POSTGRES_PASSWORD=aetherforge123

# Project Settings
PROJECTS_DIR=./projects
MAX_CONCURRENT_PROJECTS=5
PROJECT_TIMEOUT_MINUTES=30
```

## 🐳 Docker Management

### Using the Management Script

```bash
# Start all services
./manage.sh start

# Stop all services
./manage.sh stop

# Check service status
./manage.sh status

# View logs
./manage.sh logs [service]

# Health check
./manage.sh health

# Create test project
./manage.sh create-project

# Backup data
./manage.sh backup

# Clean up (removes all data!)
./manage.sh clean
```

## 📊 Monitoring

### API Endpoints
- `GET /health` - System health check
- `GET /projects` - List all projects
- `POST /projects` - Create new project
- `GET /projects/{id}` - Get project details
- `GET /components/status` - Component status
- `GET /pheromones/statistics` - Pheromone statistics
- `GET /docs` - Interactive API documentation

### Monitoring Stack (Production)
- **Prometheus**: Metrics collection at http://localhost:9090
- **Grafana**: Visualization at http://localhost:3001
- **Pheromind**: Real-time coordination at http://localhost:8502

## 🚀 Deployment

### Production Deployment

```bash
./deploy.sh production
```

### Scaling

```bash
# Scale orchestrator
docker-compose up -d --scale orchestrator=3
```

## 📚 Documentation

| Document | Description |
|----------|-------------|
| [📖 **User Guide**](USER_GUIDE.md) | Complete guide for using Aetherforge |
| [⚙️ **Installation Guide**](INSTALLATION_GUIDE.md) | Detailed setup instructions |
| [🔧 **API Reference**](API_REFERENCE.md) | Complete API documentation |
| [📋 **System Documentation**](AETHERFORGE_DOCUMENTATION.md) | Technical architecture details |

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/aetherforge)
- 📖 Documentation: [docs.aetherforge.dev](https://docs.aetherforge.dev)
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/aetherforge/issues)

---

**Aetherforge** - Where ideas become software, autonomously. ✨
