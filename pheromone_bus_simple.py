# pheromone_bus_simple.py - Simple working pheromone communication system

import json
import os
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

PHEROMONE_FILE = "pheromones.json"
MAX_PHEROMONES = 1000

def load_pheromones() -> List[Dict[str, Any]]:
    """Load pheromones from persistent storage"""
    if not os.path.exists(PHEROMONE_FILE):
        return []
    
    try:
        with open(PHEROMONE_FILE, "r") as f:
            data = json.load(f)
            return data if isinstance(data, list) else []
    except (json.JSONDecodeError, IOError):
        return []

def save_pheromones(pheromones: List[Dict[str, Any]]):
    """Save pheromones to persistent storage"""
    try:
        # Ensure directory exists
        Path(PHEROMONE_FILE).parent.mkdir(parents=True, exist_ok=True)
        
        with open(PHEROMONE_FILE, "w") as f:
            json.dump(pheromones, f, indent=2)
    except IOError:
        pass  # Fail silently for now

def drop_pheromone(signal: str, payload: Dict[str, Any], 
                  project_id: Optional[str] = None, 
                  agent_id: Optional[str] = None,
                  trail_id: Optional[str] = None,
                  priority: int = 5) -> str:
    """
    Drop a pheromone signal
    
    Args:
        signal: The signal type
        payload: The payload data
        project_id: Associated project ID
        agent_id: Agent that dropped the pheromone
        trail_id: Trail ID for grouping related pheromones
        priority: Priority level (1-10, higher is more important)
    
    Returns:
        The unique pheromone ID
    """
    pheromone_id = str(uuid.uuid4())
    timestamp = datetime.now()
    
    pheromone = {
        "id": pheromone_id,
        "timestamp": timestamp.isoformat(),
        "signal": signal,
        "payload": payload,
        "project_id": project_id,
        "agent_id": agent_id,
        "trail_id": trail_id,
        "priority": priority
    }
    
    # Load existing pheromones
    pheromones = load_pheromones()
    pheromones.append(pheromone)
    
    # Limit the number of pheromones
    if len(pheromones) > MAX_PHEROMONES:
        pheromones = pheromones[-MAX_PHEROMONES:]
    
    # Save to persistent storage
    save_pheromones(pheromones)
    
    print(f"[Pheromone] {signal} → {payload}")
    
    return pheromone_id

def get_pheromones(signal: Optional[str] = None, 
                  project_id: Optional[str] = None,
                  agent_id: Optional[str] = None,
                  trail_id: Optional[str] = None,
                  limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Get pheromones with filtering
    
    Args:
        signal: Filter by signal type
        project_id: Filter by project ID
        agent_id: Filter by agent ID
        trail_id: Filter by trail ID
        limit: Maximum number of pheromones to return
    
    Returns:
        List of matching pheromones
    """
    pheromones = load_pheromones()
    
    # Apply filters
    if signal:
        pheromones = [p for p in pheromones if p.get("signal") == signal]
    
    if project_id:
        pheromones = [p for p in pheromones if p.get("project_id") == project_id]
    
    if agent_id:
        pheromones = [p for p in pheromones if p.get("agent_id") == agent_id]
    
    if trail_id:
        pheromones = [p for p in pheromones if p.get("trail_id") == trail_id]
    
    # Sort by timestamp (newest first)
    pheromones.sort(key=lambda p: p.get("timestamp", ""), reverse=True)
    
    # Apply limit
    if limit:
        pheromones = pheromones[:limit]
    
    return pheromones

def get_statistics() -> Dict[str, Any]:
    """Get pheromone bus statistics"""
    pheromones = load_pheromones()
    
    # Count by signal type
    signal_counts = {}
    project_counts = {}
    agent_counts = {}
    
    for pheromone in pheromones:
        signal = pheromone.get("signal", "unknown")
        project_id = pheromone.get("project_id")
        agent_id = pheromone.get("agent_id")
        
        signal_counts[signal] = signal_counts.get(signal, 0) + 1
        
        if project_id:
            project_counts[project_id] = project_counts.get(project_id, 0) + 1
        
        if agent_id:
            agent_counts[agent_id] = agent_counts.get(agent_id, 0) + 1
    
    return {
        "total_pheromones": len(pheromones),
        "signal_counts": signal_counts,
        "project_counts": project_counts,
        "agent_counts": agent_counts,
        "last_updated": datetime.now().isoformat()
    }

def cleanup_old_pheromones(max_age_hours: int = 24):
    """Remove old pheromones"""
    pheromones = load_pheromones()
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    
    # Filter out old pheromones
    active_pheromones = []
    for pheromone in pheromones:
        timestamp_str = pheromone.get("timestamp")
        if timestamp_str:
            try:
                timestamp = datetime.fromisoformat(timestamp_str)
                if timestamp > cutoff_time:
                    active_pheromones.append(pheromone)
            except ValueError:
                # Keep pheromones with invalid timestamps
                active_pheromones.append(pheromone)
        else:
            # Keep pheromones without timestamps
            active_pheromones.append(pheromone)
    
    if len(active_pheromones) < len(pheromones):
        save_pheromones(active_pheromones)
        print(f"Cleaned up {len(pheromones) - len(active_pheromones)} old pheromones")

# Test function
if __name__ == "__main__":
    print("Testing simple pheromone bus...")
    
    # Test dropping a pheromone
    pheromone_id = drop_pheromone("test_signal", {"message": "Hello, world!"}, project_id="test_project")
    print(f"Dropped pheromone: {pheromone_id}")
    
    # Test getting pheromones
    pheromones = get_pheromones()
    print(f"Total pheromones: {len(pheromones)}")
    
    # Test statistics
    stats = get_statistics()
    print(f"Statistics: {stats}")
    
    print("Simple pheromone bus test completed successfully!")
