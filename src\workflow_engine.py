#!/usr/bin/env python3
"""
Full BMAD Workflow Engine for Aetherforge

This module implements a comprehensive workflow engine that can parse and execute
BMAD (Behavior-driven Multi-Agent Development) workflow YAML definitions with
support for:

- Conditional steps and branching logic
- Optional tasks with graceful failure handling
- Dynamic agent assignment and load balancing
- Parallel and sequential execution
- Retry mechanisms and timeout handling
- Real-time monitoring and progress tracking
- Integration with pheromone communication system

Features:
- YAML workflow definition parsing with validation
- State machine-based execution engine
- Conditional logic (if/else, loops, switches)
- Dynamic agent selection based on capabilities
- Error handling and recovery mechanisms
- Comprehensive logging and monitoring
- Integration with Aetherforge orchestrator
"""

import os
import yaml
import json
import asyncio
import logging
import time
import uuid
import re
import copy
import traceback
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum, IntEnum
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

class WorkflowStatus(str, Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class StepStatus(str, Enum):
    """Individual step execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    OPTIONAL_FAILED = "optional_failed"
    RETRYING = "retrying"

class StepType(str, Enum):
    """Types of workflow steps"""
    TASK = "task"
    CONDITION = "condition"
    LOOP = "loop"
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    AGENT_ASSIGNMENT = "agent_assignment"
    PHEROMONE_DROP = "pheromone_drop"
    WAIT = "wait"
    CUSTOM = "custom"

class ConditionOperator(str, Enum):
    """Conditional operators for step conditions"""
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    LESS_THAN = "lt"
    GREATER_EQUAL = "ge"
    LESS_EQUAL = "le"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    REGEX_MATCH = "regex"
    EXISTS = "exists"
    NOT_EXISTS = "not_exists"

class AgentSelectionStrategy(str, Enum):
    """Strategies for dynamic agent selection"""
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    CAPABILITY_MATCH = "capability_match"
    RANDOM = "random"
    PRIORITY_BASED = "priority_based"
    CUSTOM = "custom"

# Legacy compatibility
PhaseStatus = StepStatus

@dataclass
class WorkflowVariable:
    """Workflow variable with type and value"""
    name: str
    value: Any
    type: str = "string"
    description: str = ""
    required: bool = False
    default: Any = None

@dataclass
class StepCondition:
    """Condition for conditional step execution"""
    variable: str
    operator: ConditionOperator
    value: Any
    description: str = ""

@dataclass
class AgentRequirement:
    """Requirements for agent selection"""
    capabilities: List[str] = field(default_factory=list)
    min_performance: float = 0.0
    max_load: float = 1.0
    preferred_agents: List[str] = field(default_factory=list)
    excluded_agents: List[str] = field(default_factory=list)
    selection_strategy: AgentSelectionStrategy = AgentSelectionStrategy.CAPABILITY_MATCH

@dataclass
class RetryConfig:
    """Retry configuration for steps"""
    max_attempts: int = 3
    delay_seconds: float = 1.0
    backoff_multiplier: float = 2.0
    max_delay_seconds: float = 60.0
    retry_on_errors: List[str] = field(default_factory=list)

@dataclass
class TimeoutConfig:
    """Timeout configuration for steps"""
    execution_timeout: float = 300.0  # 5 minutes default
    agent_response_timeout: float = 30.0
    total_timeout: float = 3600.0  # 1 hour default

@dataclass
class WorkflowStep:
    """Enhanced workflow step definition"""
    id: str
    name: str
    type: StepType
    description: str = ""

    # Execution configuration
    command: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    environment: Dict[str, str] = field(default_factory=dict)

    # Conditional execution
    condition: Optional[StepCondition] = None
    depends_on: List[str] = field(default_factory=list)

    # Agent assignment
    agent_requirements: Optional[AgentRequirement] = None
    assigned_agent: Optional[str] = None

    # Error handling
    optional: bool = False
    retry_config: Optional[RetryConfig] = None
    timeout_config: Optional[TimeoutConfig] = None
    on_failure: Optional[str] = None  # Step ID to execute on failure
    on_success: Optional[str] = None  # Step ID to execute on success

    # Parallel execution
    parallel_steps: List[str] = field(default_factory=list)
    wait_for_all: bool = True

    # Loop configuration
    loop_variable: Optional[str] = None
    loop_items: List[Any] = field(default_factory=list)
    loop_condition: Optional[StepCondition] = None
    max_iterations: int = 100

    # Output handling
    output_variables: Dict[str, str] = field(default_factory=dict)
    capture_output: bool = True

    # Metadata
    tags: Set[str] = field(default_factory=set)
    priority: int = 0
    estimated_duration: float = 60.0

@dataclass
class WorkflowPhase:
    """Legacy workflow phase - converted to WorkflowStep internally"""
    id: str
    name: str
    description: str
    agent: str
    creates: List[str] = field(default_factory=list)
    requires: List[str] = field(default_factory=list)
    optional_steps: List[str] = field(default_factory=list)
    duration_minutes: int = 30
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    outputs: List[str] = field(default_factory=list)
    notes: str = ""
    condition: Optional[str] = None

    def to_workflow_step(self) -> WorkflowStep:
        """Convert legacy phase to modern workflow step"""
        return WorkflowStep(
            id=self.id,
            name=self.name,
            type=StepType.TASK,
            description=self.description,
            parameters={
                "creates": self.creates,
                "requires": self.requires,
                "optional_steps": self.optional_steps,
                "notes": self.notes
            },
            agent_requirements=AgentRequirement(
                preferred_agents=[self.agent] if self.agent else []
            ),
            estimated_duration=self.duration_minutes * 60,  # Convert to seconds
            condition=StepCondition(
                variable="legacy_condition",
                operator=ConditionOperator.EXISTS,
                value=self.condition
            ) if self.condition else None
        )

@dataclass
class WorkflowExecution:
    """Enhanced runtime execution state of a workflow"""
    id: str
    workflow_id: str
    status: WorkflowStatus
    started_at: float
    completed_at: Optional[float] = None

    # Execution context
    variables: Dict[str, WorkflowVariable] = field(default_factory=dict)
    step_results: Dict[str, Any] = field(default_factory=dict)
    step_status: Dict[str, StepStatus] = field(default_factory=dict)
    step_attempts: Dict[str, int] = field(default_factory=dict)

    # Agent assignments
    agent_assignments: Dict[str, str] = field(default_factory=dict)  # step_id -> agent_id
    agent_loads: Dict[str, float] = field(default_factory=dict)

    # Progress tracking
    total_steps: int = 0
    completed_steps: int = 0
    failed_steps: int = 0
    skipped_steps: int = 0

    # Error information
    last_error: Optional[str] = None
    error_details: Dict[str, Any] = field(default_factory=dict)

    # Performance metrics
    execution_time: float = 0.0
    agent_response_times: Dict[str, float] = field(default_factory=dict)

    # Legacy compatibility
    project_id: Optional[str] = None
    current_phase_index: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0
    phase_results: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    agent_team: Optional[Dict[str, Any]] = None
    pheromone_bus: Optional[Dict[str, Any]] = None

@dataclass
class WorkflowDefinition:
    """Enhanced workflow definition parsed from YAML"""
    id: str
    name: str
    version: str
    description: str = ""

    # Workflow metadata
    author: str = ""
    created_at: str = ""
    tags: Set[str] = field(default_factory=set)

    # Execution configuration
    variables: Dict[str, WorkflowVariable] = field(default_factory=dict)
    steps: Dict[str, WorkflowStep] = field(default_factory=dict)
    step_order: List[str] = field(default_factory=list)

    # Global configuration
    global_timeout: float = 3600.0  # 1 hour
    global_retry_config: Optional[RetryConfig] = None
    parallel_execution: bool = False
    max_concurrent_steps: int = 5

    # Agent configuration
    required_capabilities: List[str] = field(default_factory=list)
    preferred_agents: List[str] = field(default_factory=list)
    agent_selection_strategy: AgentSelectionStrategy = AgentSelectionStrategy.CAPABILITY_MATCH

    # Integration settings
    pheromone_integration: bool = True
    progress_reporting: bool = True
    detailed_logging: bool = True

    # Legacy compatibility
    type: str = "bmad"
    project_types: List[str] = field(default_factory=list)
    phases: List[WorkflowPhase] = field(default_factory=list)
    total_duration_minutes: int = 0
    flow_diagram: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

# Exception Classes
class WorkflowValidationError(Exception):
    """Raised when workflow definition validation fails"""
    def __init__(self, message: str, errors: List[str] = None):
        super().__init__(message)
        self.errors = errors or []

class WorkflowExecutionError(Exception):
    """Raised when workflow execution fails"""
    def __init__(self, message: str, step_id: str = None, error_code: str = None):
        super().__init__(message)
        self.step_id = step_id
        self.error_code = error_code

class WorkflowTimeoutError(WorkflowExecutionError):
    """Raised when workflow execution times out"""
    pass

class AgentAssignmentError(WorkflowExecutionError):
    """Raised when agent assignment fails"""
    pass

class WorkflowYAMLParser:
    """Enhanced YAML parser for BMAD workflow definitions"""

    def __init__(self):
        self.validation_errors = []

    def parse_workflow_file(self, file_path: Union[str, Path]) -> WorkflowDefinition:
        """Parse workflow definition from YAML file"""
        file_path = Path(file_path)

        if not file_path.exists():
            raise WorkflowValidationError(f"Workflow file not found: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml_content = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise WorkflowValidationError(f"Invalid YAML syntax: {e}")
        except Exception as e:
            raise WorkflowValidationError(f"Error reading workflow file: {e}")

        return self.parse_workflow_dict(yaml_content)

    def parse_workflow_dict(self, yaml_data: Dict[str, Any]) -> WorkflowDefinition:
        """Parse workflow definition from dictionary"""
        self.validation_errors = []

        try:
            # Extract basic metadata
            workflow_id = yaml_data.get('id', str(uuid.uuid4()))
            name = yaml_data.get('name', 'Unnamed Workflow')
            version = yaml_data.get('version', '1.0.0')
            description = yaml_data.get('description', '')

            # Parse metadata
            author = yaml_data.get('author', '')
            created_at = yaml_data.get('created_at', datetime.now().isoformat())
            tags = set(yaml_data.get('tags', []))

            # Parse variables
            variables = self._parse_variables(yaml_data.get('variables', {}))

            # Parse steps
            steps, step_order = self._parse_steps(yaml_data.get('steps', {}))

            # Parse global configuration
            global_config = yaml_data.get('config', {})
            global_timeout = global_config.get('timeout', 3600.0)
            global_retry_config = self._parse_retry_config(global_config.get('retry'))
            parallel_execution = global_config.get('parallel_execution', False)
            max_concurrent_steps = global_config.get('max_concurrent_steps', 5)

            # Parse agent configuration
            agent_config = yaml_data.get('agents', {})
            required_capabilities = agent_config.get('required_capabilities', [])
            preferred_agents = agent_config.get('preferred_agents', [])
            agent_selection_strategy = AgentSelectionStrategy(
                agent_config.get('selection_strategy', 'capability_match')
            )

            # Parse integration settings
            integration = yaml_data.get('integration', {})
            pheromone_integration = integration.get('pheromone_integration', True)
            progress_reporting = integration.get('progress_reporting', True)
            detailed_logging = integration.get('detailed_logging', True)

            # Legacy compatibility - parse phases if present
            if 'phases' in yaml_data:
                legacy_phases = self._parse_legacy_phases(yaml_data['phases'])
                # Convert phases to steps
                for phase in legacy_phases:
                    step = phase.to_workflow_step()
                    steps[step.id] = step
                    if step.id not in step_order:
                        step_order.append(step.id)

            # Validate the workflow
            workflow = WorkflowDefinition(
                id=workflow_id,
                name=name,
                version=version,
                description=description,
                author=author,
                created_at=created_at,
                tags=tags,
                variables=variables,
                steps=steps,
                step_order=step_order,
                global_timeout=global_timeout,
                global_retry_config=global_retry_config,
                parallel_execution=parallel_execution,
                max_concurrent_steps=max_concurrent_steps,
                required_capabilities=required_capabilities,
                preferred_agents=preferred_agents,
                agent_selection_strategy=agent_selection_strategy,
                pheromone_integration=pheromone_integration,
                progress_reporting=progress_reporting,
                detailed_logging=detailed_logging,
                # Legacy fields
                type=yaml_data.get('type', 'bmad'),
                project_types=yaml_data.get('project_types', []),
                total_duration_minutes=yaml_data.get('total_duration_minutes', 0),
                flow_diagram=yaml_data.get('flow_diagram', ''),
                metadata=yaml_data.get('metadata', {})
            )

            self._validate_workflow(workflow)

            if self.validation_errors:
                raise WorkflowValidationError(
                    "Workflow validation failed",
                    self.validation_errors
                )

            return workflow

        except WorkflowValidationError:
            raise
        except Exception as e:
            raise WorkflowValidationError(f"Error parsing workflow: {e}")

    def _parse_variables(self, variables_data: Dict[str, Any]) -> Dict[str, WorkflowVariable]:
        """Parse workflow variables"""
        variables = {}

        for name, var_data in variables_data.items():
            if isinstance(var_data, dict):
                variable = WorkflowVariable(
                    name=name,
                    value=var_data.get('value'),
                    type=var_data.get('type', 'string'),
                    description=var_data.get('description', ''),
                    required=var_data.get('required', False),
                    default=var_data.get('default')
                )
            else:
                # Simple value
                variable = WorkflowVariable(
                    name=name,
                    value=var_data,
                    type=type(var_data).__name__
                )

            variables[name] = variable

        return variables

    def _parse_steps(self, steps_data: Dict[str, Any]) -> Tuple[Dict[str, WorkflowStep], List[str]]:
        """Parse workflow steps"""
        steps = {}
        step_order = []

        for step_id, step_data in steps_data.items():
            try:
                step = self._parse_single_step(step_id, step_data)
                steps[step_id] = step
                step_order.append(step_id)
            except Exception as e:
                self.validation_errors.append(f"Error parsing step '{step_id}': {e}")

        return steps, step_order

    def _parse_single_step(self, step_id: str, step_data: Dict[str, Any]) -> WorkflowStep:
        """Parse a single workflow step"""
        name = step_data.get('name', step_id)
        step_type = StepType(step_data.get('type', 'task'))
        description = step_data.get('description', '')

        # Parse execution configuration
        command = step_data.get('command')
        parameters = step_data.get('parameters', {})
        environment = step_data.get('environment', {})

        # Parse conditional execution
        condition = self._parse_condition(step_data.get('condition'))
        depends_on = step_data.get('depends_on', [])
        if isinstance(depends_on, str):
            depends_on = [depends_on]

        # Parse agent requirements
        agent_requirements = self._parse_agent_requirements(step_data.get('agent'))
        assigned_agent = step_data.get('assigned_agent')

        # Parse error handling
        optional = step_data.get('optional', False)
        retry_config = self._parse_retry_config(step_data.get('retry'))
        timeout_config = self._parse_timeout_config(step_data.get('timeout'))
        on_failure = step_data.get('on_failure')
        on_success = step_data.get('on_success')

        # Parse parallel execution
        parallel_steps = step_data.get('parallel_steps', [])
        wait_for_all = step_data.get('wait_for_all', True)

        # Parse loop configuration
        loop_variable = step_data.get('loop_variable')
        loop_items = step_data.get('loop_items', [])
        loop_condition = self._parse_condition(step_data.get('loop_condition'))
        max_iterations = step_data.get('max_iterations', 100)

        # Parse output handling
        output_variables = step_data.get('output_variables', {})
        capture_output = step_data.get('capture_output', True)

        # Parse metadata
        tags = set(step_data.get('tags', []))
        priority = step_data.get('priority', 0)
        estimated_duration = step_data.get('estimated_duration', 60.0)

        return WorkflowStep(
            id=step_id,
            name=name,
            type=step_type,
            description=description,
            command=command,
            parameters=parameters,
            environment=environment,
            condition=condition,
            depends_on=depends_on,
            agent_requirements=agent_requirements,
            assigned_agent=assigned_agent,
            optional=optional,
            retry_config=retry_config,
            timeout_config=timeout_config,
            on_failure=on_failure,
            on_success=on_success,
            parallel_steps=parallel_steps,
            wait_for_all=wait_for_all,
            loop_variable=loop_variable,
            loop_items=loop_items,
            loop_condition=loop_condition,
            max_iterations=max_iterations,
            output_variables=output_variables,
            capture_output=capture_output,
            tags=tags,
            priority=priority,
            estimated_duration=estimated_duration
        )

    def _parse_condition(self, condition_data: Any) -> Optional[StepCondition]:
        """Parse step condition"""
        if not condition_data:
            return None

        if isinstance(condition_data, str):
            # Simple existence check
            return StepCondition(
                variable=condition_data,
                operator=ConditionOperator.EXISTS,
                value=True
            )

        if isinstance(condition_data, dict):
            variable = condition_data.get('variable', '')
            operator = ConditionOperator(condition_data.get('operator', 'eq'))
            value = condition_data.get('value')
            description = condition_data.get('description', '')

            return StepCondition(
                variable=variable,
                operator=operator,
                value=value,
                description=description
            )

        return None

    def _parse_agent_requirements(self, agent_data: Any) -> Optional[AgentRequirement]:
        """Parse agent requirements"""
        if not agent_data:
            return None

        if isinstance(agent_data, str):
            # Simple agent preference
            return AgentRequirement(
                preferred_agents=[agent_data]
            )

        if isinstance(agent_data, dict):
            capabilities = agent_data.get('capabilities', [])
            min_performance = agent_data.get('min_performance', 0.0)
            max_load = agent_data.get('max_load', 1.0)
            preferred_agents = agent_data.get('preferred_agents', [])
            excluded_agents = agent_data.get('excluded_agents', [])
            selection_strategy = AgentSelectionStrategy(
                agent_data.get('selection_strategy', 'capability_match')
            )

            return AgentRequirement(
                capabilities=capabilities,
                min_performance=min_performance,
                max_load=max_load,
                preferred_agents=preferred_agents,
                excluded_agents=excluded_agents,
                selection_strategy=selection_strategy
            )

        return None

    def _parse_retry_config(self, retry_data: Any) -> Optional[RetryConfig]:
        """Parse retry configuration"""
        if not retry_data:
            return None

        if isinstance(retry_data, int):
            # Simple max attempts
            return RetryConfig(max_attempts=retry_data)

        if isinstance(retry_data, dict):
            return RetryConfig(
                max_attempts=retry_data.get('max_attempts', 3),
                delay_seconds=retry_data.get('delay_seconds', 1.0),
                backoff_multiplier=retry_data.get('backoff_multiplier', 2.0),
                max_delay_seconds=retry_data.get('max_delay_seconds', 60.0),
                retry_on_errors=retry_data.get('retry_on_errors', [])
            )

        return None

    def _parse_timeout_config(self, timeout_data: Any) -> Optional[TimeoutConfig]:
        """Parse timeout configuration"""
        if not timeout_data:
            return None

        if isinstance(timeout_data, (int, float)):
            # Simple execution timeout
            return TimeoutConfig(execution_timeout=float(timeout_data))

        if isinstance(timeout_data, dict):
            return TimeoutConfig(
                execution_timeout=timeout_data.get('execution_timeout', 300.0),
                agent_response_timeout=timeout_data.get('agent_response_timeout', 30.0),
                total_timeout=timeout_data.get('total_timeout', 3600.0)
            )

        return None

    def _parse_legacy_phases(self, phases_data: List[Dict[str, Any]]) -> List[WorkflowPhase]:
        """Parse legacy workflow phases for backward compatibility"""
        phases = []

        for phase_data in phases_data:
            try:
                phase = WorkflowPhase(
                    id=phase_data.get('id', str(uuid.uuid4())),
                    name=phase_data.get('name', 'Unnamed Phase'),
                    description=phase_data.get('description', ''),
                    agent=phase_data.get('agent', ''),
                    creates=phase_data.get('creates', []),
                    requires=phase_data.get('requires', []),
                    optional_steps=phase_data.get('optional_steps', []),
                    duration_minutes=phase_data.get('duration_minutes', 30),
                    notes=phase_data.get('notes', ''),
                    condition=phase_data.get('condition')
                )
                phases.append(phase)
            except Exception as e:
                self.validation_errors.append(f"Error parsing legacy phase: {e}")

        return phases

    def _validate_workflow(self, workflow: WorkflowDefinition) -> None:
        """Validate workflow definition"""
        # Check required fields
        if not workflow.name:
            self.validation_errors.append("Workflow name is required")

        if not workflow.steps and not workflow.phases:
            self.validation_errors.append("Workflow must have at least one step or phase")

        # Validate step dependencies
        for step_id, step in workflow.steps.items():
            for dep in step.depends_on:
                if dep not in workflow.steps:
                    self.validation_errors.append(
                        f"Step '{step_id}' depends on non-existent step '{dep}'"
                    )

        # Check for circular dependencies
        self._check_circular_dependencies(workflow.steps)

        # Validate conditions reference existing variables
        for step_id, step in workflow.steps.items():
            if step.condition:
                if step.condition.variable not in workflow.variables:
                    self.validation_errors.append(
                        f"Step '{step_id}' condition references undefined variable '{step.condition.variable}'"
                    )

        # Validate agent requirements
        for step_id, step in workflow.steps.items():
            if step.agent_requirements:
                # Check if required capabilities are reasonable
                if len(step.agent_requirements.capabilities) > 10:
                    self.validation_errors.append(
                        f"Step '{step_id}' has too many required capabilities (max 10)"
                    )

    def _check_circular_dependencies(self, steps: Dict[str, WorkflowStep]) -> None:
        """Check for circular dependencies in workflow steps"""
        def has_cycle(step_id: str, visited: Set[str], rec_stack: Set[str]) -> bool:
            visited.add(step_id)
            rec_stack.add(step_id)

            step = steps.get(step_id)
            if step:
                for dep in step.depends_on:
                    if dep not in visited:
                        if has_cycle(dep, visited, rec_stack):
                            return True
                    elif dep in rec_stack:
                        return True

            rec_stack.remove(step_id)
            return False

        visited = set()
        for step_id in steps:
            if step_id not in visited:
                if has_cycle(step_id, visited, set()):
                    self.validation_errors.append(
                        f"Circular dependency detected involving step '{step_id}'"
                    )

class WorkflowExecutionEngine:
    """Enhanced workflow execution engine with comprehensive features"""

    def __init__(self, pheromone_bus=None):
        self.pheromone_bus = pheromone_bus
        self.active_executions: Dict[str, WorkflowExecution] = {}
        self.agent_registry: Dict[str, Dict[str, Any]] = {}
        self.execution_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=10)

        # Performance tracking
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0
        }

    async def start_workflow(
        self,
        workflow_id: str,
        project_id: str,
        agent_team: Dict[str, Any],
        pheromone_bus: Dict[str, Any],
        context: Dict[str, Any]
    ) -> WorkflowExecution:
        """Start workflow execution with enhanced context"""

        # Load workflow definition
        workflow = self.get_registered_workflow(workflow_id)
        if not workflow:
            workflow = await self._load_workflow_definition(workflow_id)
        if not workflow:
            raise WorkflowExecutionError(f"Workflow '{workflow_id}' not found")

        # Create execution instance
        execution = WorkflowExecution(
            id=str(uuid.uuid4()),
            workflow_id=workflow_id,
            status=WorkflowStatus.RUNNING,
            started_at=time.time(),
            project_id=project_id,
            agent_team=agent_team,
            pheromone_bus=pheromone_bus
        )

        # Initialize execution context
        await self._initialize_execution_context(execution, workflow, context)

        # Register execution
        with self.execution_lock:
            self.active_executions[execution.id] = execution
            self.execution_stats["total_executions"] += 1

        # Start execution in background
        asyncio.create_task(self._execute_workflow(execution, workflow))

        logger.info(f"Started workflow execution {execution.id} for workflow {workflow_id}")
        return execution

    async def _load_workflow_definition(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Load workflow definition by ID"""
        # First check if it's a built-in workflow
        builtin_workflow = await self._get_builtin_workflow(workflow_id)
        if builtin_workflow:
            return builtin_workflow

        # Try to load from various sources
        workflow_paths = [
            Path(f"workflows/{workflow_id}.yaml"),
            Path(f"workflows/{workflow_id}.yml"),
            Path(f"src/workflows/{workflow_id}.yaml"),
            Path(f"config/workflows/{workflow_id}.yaml")
        ]

        parser = WorkflowYAMLParser()

        for path in workflow_paths:
            if path.exists():
                try:
                    return parser.parse_workflow_file(path)
                except Exception as e:
                    logger.warning(f"Failed to load workflow from {path}: {e}")

        return None

    def register_workflow(self, workflow: WorkflowDefinition) -> None:
        """Register a workflow definition for execution"""
        self._registered_workflows = getattr(self, '_registered_workflows', {})
        self._registered_workflows[workflow.id] = workflow

    def get_registered_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get a registered workflow definition"""
        registered = getattr(self, '_registered_workflows', {})
        return registered.get(workflow_id)

    async def _get_builtin_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get built-in workflow definition"""
        builtin_workflows = {
            "greenfield-fullstack": self._create_greenfield_fullstack_workflow(),
            "enhancement": self._create_enhancement_workflow(),
            "debugging": self._create_debugging_workflow(),
            "testing": self._create_testing_workflow()
        }

        return builtin_workflows.get(workflow_id)

    def _create_greenfield_fullstack_workflow(self) -> WorkflowDefinition:
        """Create built-in greenfield fullstack workflow"""
        steps = {
            "analyze_requirements": WorkflowStep(
                id="analyze_requirements",
                name="Analyze Requirements",
                type=StepType.TASK,
                description="Analyze project requirements and create specifications",
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "requirements"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=300.0,  # 5 minutes
                output_variables={"requirements": "analysis_result"}
            ),
            "design_architecture": WorkflowStep(
                id="design_architecture",
                name="Design Architecture",
                type=StepType.TASK,
                description="Design system architecture and technology stack",
                depends_on=["analyze_requirements"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "design"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"architecture": "design_result"}
            ),
            "generate_backend": WorkflowStep(
                id="generate_backend",
                name="Generate Backend",
                type=StepType.TASK,
                description="Generate backend code and API endpoints",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "api", "database"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=900.0,  # 15 minutes
                output_variables={"backend_files": "backend_result"}
            ),
            "generate_frontend": WorkflowStep(
                id="generate_frontend",
                name="Generate Frontend",
                type=StepType.TASK,
                description="Generate frontend code and user interface",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "ui", "react"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=900.0,  # 15 minutes
                output_variables={"frontend_files": "frontend_result"}
            ),
            "integrate_components": WorkflowStep(
                id="integrate_components",
                name="Integrate Components",
                type=StepType.TASK,
                description="Integrate frontend and backend components",
                depends_on=["generate_backend", "generate_frontend"],
                agent_requirements=AgentRequirement(
                    capabilities=["integration", "fullstack"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"integration_result": "integration_status"}
            ),
            "generate_tests": WorkflowStep(
                id="generate_tests",
                name="Generate Tests",
                type=StepType.TASK,
                description="Generate comprehensive test suites",
                depends_on=["integrate_components"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "qa"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"test_files": "test_result"}
            ),
            "create_documentation": WorkflowStep(
                id="create_documentation",
                name="Create Documentation",
                type=StepType.TASK,
                description="Create project documentation and README",
                depends_on=["integrate_components"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["documentation", "writing"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=300.0,  # 5 minutes
                output_variables={"documentation": "docs_result"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Fullstack Development",
            version="1.0.0",
            description="Complete workflow for creating a new fullstack application from scratch",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600.0,  # 1 hour
            parallel_execution=True,
            max_concurrent_steps=3,
            required_capabilities=["analysis", "architecture", "backend", "frontend"],
            pheromone_integration=True,
            progress_reporting=True
        )

    def _create_enhancement_workflow(self) -> WorkflowDefinition:
        """Create built-in enhancement workflow"""
        steps = {
            "analyze_existing": WorkflowStep(
                id="analyze_existing",
                name="Analyze Existing Code",
                type=StepType.TASK,
                description="Analyze existing codebase and identify enhancement opportunities",
                agent_requirements=AgentRequirement(capabilities=["analysis", "code_review"]),
                estimated_duration=600.0
            ),
            "plan_enhancements": WorkflowStep(
                id="plan_enhancements",
                name="Plan Enhancements",
                type=StepType.TASK,
                description="Create enhancement plan and implementation strategy",
                depends_on=["analyze_existing"],
                agent_requirements=AgentRequirement(capabilities=["planning", "architecture"]),
                estimated_duration=300.0
            ),
            "implement_changes": WorkflowStep(
                id="implement_changes",
                name="Implement Changes",
                type=StepType.TASK,
                description="Implement the planned enhancements",
                depends_on=["plan_enhancements"],
                agent_requirements=AgentRequirement(capabilities=["development", "refactoring"]),
                estimated_duration=1200.0
            ),
            "test_changes": WorkflowStep(
                id="test_changes",
                name="Test Changes",
                type=StepType.TASK,
                description="Test the implemented changes",
                depends_on=["implement_changes"],
                agent_requirements=AgentRequirement(capabilities=["testing", "qa"]),
                estimated_duration=600.0
            )
        }

        return WorkflowDefinition(
            id="enhancement",
            name="Code Enhancement Workflow",
            version="1.0.0",
            description="Workflow for enhancing existing codebases",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=2400.0,  # 40 minutes
            required_capabilities=["analysis", "development", "testing"]
        )

    def _create_debugging_workflow(self) -> WorkflowDefinition:
        """Create built-in debugging workflow"""
        steps = {
            "identify_issue": WorkflowStep(
                id="identify_issue",
                name="Identify Issue",
                type=StepType.TASK,
                description="Identify and analyze the reported issue",
                agent_requirements=AgentRequirement(capabilities=["debugging", "analysis"]),
                estimated_duration=300.0
            ),
            "reproduce_bug": WorkflowStep(
                id="reproduce_bug",
                name="Reproduce Bug",
                type=StepType.TASK,
                description="Reproduce the bug in a controlled environment",
                depends_on=["identify_issue"],
                agent_requirements=AgentRequirement(capabilities=["debugging", "testing"]),
                estimated_duration=600.0,
                optional=True
            ),
            "fix_issue": WorkflowStep(
                id="fix_issue",
                name="Fix Issue",
                type=StepType.TASK,
                description="Implement fix for the identified issue",
                depends_on=["identify_issue"],
                agent_requirements=AgentRequirement(capabilities=["development", "debugging"]),
                estimated_duration=900.0
            ),
            "verify_fix": WorkflowStep(
                id="verify_fix",
                name="Verify Fix",
                type=StepType.TASK,
                description="Verify that the fix resolves the issue",
                depends_on=["fix_issue"],
                agent_requirements=AgentRequirement(capabilities=["testing", "qa"]),
                estimated_duration=300.0
            )
        }

        return WorkflowDefinition(
            id="debugging",
            name="Bug Fixing Workflow",
            version="1.0.0",
            description="Workflow for identifying and fixing bugs",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=1800.0,  # 30 minutes
            required_capabilities=["debugging", "development", "testing"]
        )

    def _create_testing_workflow(self) -> WorkflowDefinition:
        """Create built-in testing workflow"""
        steps = {
            "analyze_code": WorkflowStep(
                id="analyze_code",
                name="Analyze Code for Testing",
                type=StepType.TASK,
                description="Analyze code to determine testing strategy",
                agent_requirements=AgentRequirement(capabilities=["analysis", "testing"]),
                estimated_duration=300.0
            ),
            "generate_unit_tests": WorkflowStep(
                id="generate_unit_tests",
                name="Generate Unit Tests",
                type=StepType.TASK,
                description="Generate comprehensive unit tests",
                depends_on=["analyze_code"],
                agent_requirements=AgentRequirement(capabilities=["testing", "unit_tests"]),
                estimated_duration=600.0
            ),
            "generate_integration_tests": WorkflowStep(
                id="generate_integration_tests",
                name="Generate Integration Tests",
                type=StepType.TASK,
                description="Generate integration tests",
                depends_on=["analyze_code"],
                agent_requirements=AgentRequirement(capabilities=["testing", "integration_tests"]),
                estimated_duration=600.0,
                optional=True
            ),
            "run_tests": WorkflowStep(
                id="run_tests",
                name="Run Test Suite",
                type=StepType.TASK,
                description="Execute all generated tests",
                depends_on=["generate_unit_tests"],
                agent_requirements=AgentRequirement(capabilities=["testing", "execution"]),
                estimated_duration=300.0
            )
        }

        return WorkflowDefinition(
            id="testing",
            name="Comprehensive Testing Workflow",
            version="1.0.0",
            description="Workflow for generating and running comprehensive tests",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=1500.0,  # 25 minutes
            required_capabilities=["testing", "analysis"]
        )

    async def _initialize_execution_context(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        context: Dict[str, Any]
    ) -> None:
        """Initialize execution context with variables and state"""

        # Initialize workflow variables
        for name, var in workflow.variables.items():
            execution.variables[name] = var

        # Add context variables
        for name, value in context.items():
            if name not in execution.variables:
                execution.variables[name] = WorkflowVariable(
                    name=name,
                    value=value,
                    type=type(value).__name__
                )

        # Initialize step status
        for step_id in workflow.steps:
            execution.step_status[step_id] = StepStatus.PENDING
            execution.step_attempts[step_id] = 0

        # Set total steps
        execution.total_steps = len(workflow.steps)

        logger.info(f"Initialized execution context for {execution.id}")

    async def _execute_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Main workflow execution loop"""

        try:
            logger.info(f"Starting workflow execution {execution.id}")

            # Drop pheromone for workflow start
            await self._drop_pheromone("workflow_started", {
                "execution_id": execution.id,
                "workflow_id": workflow.id,
                "workflow_name": workflow.name
            }, execution.project_id)

            # Execute steps based on workflow configuration
            if workflow.parallel_execution:
                await self._execute_parallel_workflow(execution, workflow)
            else:
                await self._execute_sequential_workflow(execution, workflow)

            # Check final status
            if execution.failed_steps > 0:
                execution.status = WorkflowStatus.FAILED
                execution.last_error = f"Workflow failed with {execution.failed_steps} failed steps"
            else:
                execution.status = WorkflowStatus.COMPLETED

            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            # Update statistics
            with self.execution_lock:
                if execution.status == WorkflowStatus.COMPLETED:
                    self.execution_stats["successful_executions"] += 1
                else:
                    self.execution_stats["failed_executions"] += 1

                # Update average execution time
                total_executions = self.execution_stats["total_executions"]
                current_avg = self.execution_stats["average_execution_time"]
                new_avg = ((current_avg * (total_executions - 1)) + execution.execution_time) / total_executions
                self.execution_stats["average_execution_time"] = new_avg

            # Drop completion pheromone
            await self._drop_pheromone("workflow_completed", {
                "execution_id": execution.id,
                "status": execution.status.value,
                "execution_time": execution.execution_time,
                "completed_steps": execution.completed_steps,
                "failed_steps": execution.failed_steps
            }, execution.project_id)

            logger.info(f"Workflow execution {execution.id} completed with status {execution.status}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = str(e)
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            logger.error(f"Workflow execution {execution.id} failed: {e}")

            # Drop error pheromone
            await self._drop_pheromone("workflow_failed", {
                "execution_id": execution.id,
                "error": str(e),
                "execution_time": execution.execution_time
            }, execution.project_id)

        finally:
            # Clean up execution
            with self.execution_lock:
                self.active_executions.pop(execution.id, None)

    async def _execute_sequential_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Execute workflow steps sequentially"""

        for step_id in workflow.step_order:
            step = workflow.steps[step_id]

            # Check if step should be executed
            if not await self._should_execute_step(step, execution, workflow):
                execution.step_status[step_id] = StepStatus.SKIPPED
                execution.skipped_steps += 1
                continue

            # Wait for dependencies
            await self._wait_for_dependencies(step, execution)

            # Execute step
            success = await self._execute_step(step, execution, workflow)

            if success:
                execution.completed_steps += 1
            else:
                execution.failed_steps += 1

                # Check if workflow should continue
                if not step.optional and not workflow.global_retry_config:
                    break

    async def _execute_parallel_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Execute workflow steps in parallel where possible"""

        pending_steps = set(workflow.step_order)
        running_tasks = {}
        max_concurrent = workflow.max_concurrent_steps
        max_iterations = 100  # Prevent infinite loops
        iteration = 0

        while (pending_steps or running_tasks) and iteration < max_iterations:
            iteration += 1

            # Start new tasks if we have capacity
            started_new_task = False
            while len(running_tasks) < max_concurrent and pending_steps:
                # Find a step that can be started
                ready_step = None
                for step_id in list(pending_steps):  # Create a copy to avoid modification during iteration
                    step = workflow.steps[step_id]
                    if await self._are_dependencies_satisfied(step, execution):
                        ready_step = step_id
                        break

                if ready_step:
                    step = workflow.steps[ready_step]
                    pending_steps.remove(ready_step)

                    # Check if step should be executed
                    if await self._should_execute_step(step, execution, workflow):
                        task = asyncio.create_task(self._execute_step(step, execution, workflow))
                        running_tasks[ready_step] = task
                        started_new_task = True
                    else:
                        execution.step_status[ready_step] = StepStatus.SKIPPED
                        execution.skipped_steps += 1
                        started_new_task = True
                else:
                    # No ready steps available
                    break

            # Wait for at least one task to complete if we have running tasks
            if running_tasks:
                try:
                    done, pending_tasks = await asyncio.wait(
                        running_tasks.values(),
                        return_when=asyncio.FIRST_COMPLETED,
                        timeout=1.0  # Add timeout to prevent hanging
                    )

                    # Process completed tasks
                    for task in done:
                        # Find which step this task belongs to
                        step_id = None
                        for sid, t in running_tasks.items():
                            if t == task:
                                step_id = sid
                                break

                        if step_id:
                            try:
                                success = await task
                                del running_tasks[step_id]

                                if success:
                                    execution.completed_steps += 1
                                else:
                                    execution.failed_steps += 1
                            except Exception as e:
                                logger.error(f"Task for step {step_id} failed: {e}")
                                del running_tasks[step_id]
                                execution.failed_steps += 1

                except asyncio.TimeoutError:
                    # Timeout waiting for tasks - continue to next iteration
                    pass
            elif not started_new_task and pending_steps:
                # No tasks running and no new tasks started - might be deadlock
                logger.warning(f"Potential deadlock detected. Pending steps: {pending_steps}")
                # Skip remaining steps to avoid infinite loop
                for step_id in pending_steps:
                    execution.step_status[step_id] = StepStatus.SKIPPED
                    execution.skipped_steps += 1
                break

        if iteration >= max_iterations:
            logger.warning("Parallel workflow execution reached maximum iterations")

        # Wait for any remaining tasks to complete
        if running_tasks:
            try:
                await asyncio.wait(running_tasks.values(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within timeout")

    async def _should_execute_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Check if a step should be executed based on conditions"""

        # Check step condition
        if step.condition:
            if not await self._evaluate_condition(step.condition, execution):
                return False

        # Check dependencies
        if not await self._are_dependencies_satisfied(step, execution):
            return False

        return True

    async def _evaluate_condition(self, condition: StepCondition, execution: WorkflowExecution) -> bool:
        """Evaluate a step condition"""

        # Get variable value
        variable = execution.variables.get(condition.variable)
        if variable is None:
            # Variable doesn't exist
            if condition.operator in [ConditionOperator.EXISTS, ConditionOperator.NOT_EXISTS]:
                return condition.operator == ConditionOperator.NOT_EXISTS
            return False

        var_value = variable.value
        condition_value = condition.value

        # Evaluate based on operator
        if condition.operator == ConditionOperator.EQUALS:
            return var_value == condition_value
        elif condition.operator == ConditionOperator.NOT_EQUALS:
            return var_value != condition_value
        elif condition.operator == ConditionOperator.GREATER_THAN:
            return var_value > condition_value
        elif condition.operator == ConditionOperator.LESS_THAN:
            return var_value < condition_value
        elif condition.operator == ConditionOperator.GREATER_EQUAL:
            return var_value >= condition_value
        elif condition.operator == ConditionOperator.LESS_EQUAL:
            return var_value <= condition_value
        elif condition.operator == ConditionOperator.CONTAINS:
            return condition_value in str(var_value)
        elif condition.operator == ConditionOperator.NOT_CONTAINS:
            return condition_value not in str(var_value)
        elif condition.operator == ConditionOperator.REGEX_MATCH:
            import re
            return bool(re.search(str(condition_value), str(var_value)))
        elif condition.operator == ConditionOperator.EXISTS:
            return True  # Variable exists
        elif condition.operator == ConditionOperator.NOT_EXISTS:
            return False  # Variable exists

        return False

    async def _are_dependencies_satisfied(self, step: WorkflowStep, execution: WorkflowExecution) -> bool:
        """Check if all step dependencies are satisfied"""

        for dep_id in step.depends_on:
            dep_status = execution.step_status.get(dep_id, StepStatus.PENDING)
            if dep_status not in [StepStatus.COMPLETED, StepStatus.SKIPPED]:
                return False

        return True

    async def _wait_for_dependencies(self, step: WorkflowStep, execution: WorkflowExecution) -> None:
        """Wait for step dependencies to complete"""

        while not await self._are_dependencies_satisfied(step, execution):
            await asyncio.sleep(0.1)  # Small delay to prevent busy waiting

    async def _execute_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a single workflow step with retry logic"""

        execution.step_status[step.id] = StepStatus.RUNNING

        # Drop pheromone for step start
        await self._drop_pheromone("step_started", {
            "step_id": step.id,
            "step_name": step.name,
            "execution_id": execution.id
        }, execution.project_id)

        max_attempts = 1
        if step.retry_config:
            max_attempts = step.retry_config.max_attempts
        elif workflow.global_retry_config:
            max_attempts = workflow.global_retry_config.max_attempts

        for attempt in range(max_attempts):
            execution.step_attempts[step.id] = attempt + 1

            try:
                # Execute based on step type
                if step.type == StepType.TASK:
                    success = await self._execute_task_step(step, execution, workflow)
                elif step.type == StepType.CONDITION:
                    success = await self._execute_condition_step(step, execution, workflow)
                elif step.type == StepType.LOOP:
                    success = await self._execute_loop_step(step, execution, workflow)
                elif step.type == StepType.PARALLEL:
                    success = await self._execute_parallel_step(step, execution, workflow)
                elif step.type == StepType.AGENT_ASSIGNMENT:
                    success = await self._execute_agent_assignment_step(step, execution, workflow)
                elif step.type == StepType.PHEROMONE_DROP:
                    success = await self._execute_pheromone_step(step, execution, workflow)
                elif step.type == StepType.WAIT:
                    success = await self._execute_wait_step(step, execution, workflow)
                else:
                    success = await self._execute_custom_step(step, execution, workflow)

                if success:
                    execution.step_status[step.id] = StepStatus.COMPLETED

                    # Handle output variables
                    await self._handle_step_outputs(step, execution)

                    # Drop success pheromone
                    await self._drop_pheromone("step_completed", {
                        "step_id": step.id,
                        "step_name": step.name,
                        "execution_id": execution.id,
                        "attempt": attempt + 1
                    }, execution.project_id)

                    return True
                else:
                    # Step failed, check if we should retry
                    if attempt < max_attempts - 1:
                        execution.step_status[step.id] = StepStatus.RETRYING

                        # Calculate retry delay
                        delay = self._calculate_retry_delay(step, workflow, attempt)
                        await asyncio.sleep(delay)

                        # Drop retry pheromone
                        await self._drop_pheromone("step_retrying", {
                            "step_id": step.id,
                            "step_name": step.name,
                            "execution_id": execution.id,
                            "attempt": attempt + 1,
                            "next_attempt_in": delay
                        }, execution.project_id)
                    else:
                        # All attempts failed
                        if step.optional:
                            execution.step_status[step.id] = StepStatus.OPTIONAL_FAILED
                        else:
                            execution.step_status[step.id] = StepStatus.FAILED

                        # Drop failure pheromone
                        await self._drop_pheromone("step_failed", {
                            "step_id": step.id,
                            "step_name": step.name,
                            "execution_id": execution.id,
                            "attempts": max_attempts,
                            "optional": step.optional
                        }, execution.project_id)

                        return step.optional  # Return True for optional steps

            except Exception as e:
                logger.error(f"Error executing step {step.id}: {e}")
                execution.last_error = str(e)

                if attempt < max_attempts - 1:
                    # Retry on exception
                    execution.step_status[step.id] = StepStatus.RETRYING
                    delay = self._calculate_retry_delay(step, workflow, attempt)
                    await asyncio.sleep(delay)
                else:
                    # Final failure
                    if step.optional:
                        execution.step_status[step.id] = StepStatus.OPTIONAL_FAILED
                        return True
                    else:
                        execution.step_status[step.id] = StepStatus.FAILED
                        return False

        return False

    def _calculate_retry_delay(self, step: WorkflowStep, workflow: WorkflowDefinition, attempt: int) -> float:
        """Calculate retry delay with exponential backoff"""

        retry_config = step.retry_config or workflow.global_retry_config
        if not retry_config:
            return 1.0

        delay = retry_config.delay_seconds * (retry_config.backoff_multiplier ** attempt)
        return min(delay, retry_config.max_delay_seconds)

    async def _execute_task_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a task step"""

        try:
            # Assign agent if needed
            agent_id = await self._assign_agent_to_step(step, execution)
            if not agent_id:
                logger.error(f"Failed to assign agent to step {step.id}")
                return False

            execution.agent_assignments[step.id] = agent_id

            # Prepare step context
            step_context = {
                "step_id": step.id,
                "step_name": step.name,
                "command": step.command,
                "parameters": step.parameters,
                "environment": step.environment,
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "variables": {name: var.value for name, var in execution.variables.items()}
            }

            # Execute step through agent
            start_time = time.time()
            result = await self._execute_step_with_agent(agent_id, step_context)
            end_time = time.time()

            execution.agent_response_times[step.id] = end_time - start_time
            execution.step_results[step.id] = result

            return result.get("success", False) if isinstance(result, dict) else bool(result)

        except Exception as e:
            logger.error(f"Task step {step.id} execution failed: {e}")
            execution.step_results[step.id] = {"success": False, "error": str(e)}
            return False

    async def _execute_condition_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a condition step"""

        if step.condition:
            result = await self._evaluate_condition(step.condition, execution)
            execution.step_results[step.id] = {"condition_result": result}
            return result

        return True

    async def _execute_loop_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a loop step"""

        try:
            iteration = 0
            loop_results = []

            # Loop over items if specified
            if step.loop_items:
                for item in step.loop_items:
                    if iteration >= step.max_iterations:
                        break

                    # Set loop variable
                    if step.loop_variable:
                        execution.variables[step.loop_variable] = WorkflowVariable(
                            name=step.loop_variable,
                            value=item,
                            type=type(item).__name__
                        )

                    # Execute loop body (would need to be defined in step parameters)
                    loop_result = await self._execute_loop_iteration(step, execution, iteration)
                    loop_results.append(loop_result)

                    iteration += 1

            # Loop with condition
            elif step.loop_condition:
                while iteration < step.max_iterations:
                    if not await self._evaluate_condition(step.loop_condition, execution):
                        break

                    loop_result = await self._execute_loop_iteration(step, execution, iteration)
                    loop_results.append(loop_result)

                    iteration += 1

            execution.step_results[step.id] = {
                "iterations": iteration,
                "results": loop_results
            }

            return True

        except Exception as e:
            logger.error(f"Loop step {step.id} execution failed: {e}")
            return False

    async def _execute_loop_iteration(self, step: WorkflowStep, execution: WorkflowExecution, iteration: int) -> Any:
        """Execute a single loop iteration"""

        # This would execute the loop body defined in step parameters
        # For now, return a simple result
        return {
            "iteration": iteration,
            "timestamp": time.time(),
            "success": True
        }

    async def _execute_parallel_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute parallel sub-steps"""

        if not step.parallel_steps:
            return True

        try:
            # Create tasks for parallel steps
            tasks = []
            for parallel_step_id in step.parallel_steps:
                if parallel_step_id in workflow.steps:
                    parallel_step = workflow.steps[parallel_step_id]
                    task = asyncio.create_task(
                        self._execute_step(parallel_step, execution, workflow)
                    )
                    tasks.append((parallel_step_id, task))

            # Wait for completion
            if step.wait_for_all:
                # Wait for all tasks to complete
                results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
                success = all(isinstance(result, bool) and result for result in results)
            else:
                # Wait for first successful completion
                done, pending = await asyncio.wait(
                    [task for _, task in tasks],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # Cancel pending tasks
                for task in pending:
                    task.cancel()

                # Check if any task succeeded
                success = any(task.result() for task in done if not task.exception())

            execution.step_results[step.id] = {
                "parallel_steps": step.parallel_steps,
                "wait_for_all": step.wait_for_all,
                "success": success
            }

            return success

        except Exception as e:
            logger.error(f"Parallel step {step.id} execution failed: {e}")
            return False

    async def _execute_agent_assignment_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute agent assignment step"""

        try:
            # This step type is used to dynamically assign agents to subsequent steps
            agent_id = await self._assign_agent_to_step(step, execution)

            if agent_id:
                # Store agent assignment for future steps
                target_steps = step.parameters.get("target_steps", [])
                for target_step_id in target_steps:
                    execution.agent_assignments[target_step_id] = agent_id

                execution.step_results[step.id] = {
                    "assigned_agent": agent_id,
                    "target_steps": target_steps
                }
                return True

            return False

        except Exception as e:
            logger.error(f"Agent assignment step {step.id} failed: {e}")
            return False

    async def _execute_pheromone_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute pheromone drop step"""

        try:
            signal_type = step.parameters.get("signal_type", "custom")
            payload = step.parameters.get("payload", {})

            # Add execution context to payload
            payload.update({
                "step_id": step.id,
                "execution_id": execution.id,
                "timestamp": time.time()
            })

            await self._drop_pheromone(signal_type, payload, execution.project_id)

            execution.step_results[step.id] = {
                "signal_type": signal_type,
                "payload": payload
            }

            return True

        except Exception as e:
            logger.error(f"Pheromone step {step.id} failed: {e}")
            return False

    async def _execute_wait_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute wait step"""

        try:
            wait_time = step.parameters.get("wait_time", 1.0)
            wait_condition = step.parameters.get("wait_condition")
            max_wait_time = step.parameters.get("max_wait_time", 300.0)  # 5 minutes default

            start_time = time.time()

            if wait_condition:
                # Wait for condition to be true
                while time.time() - start_time < max_wait_time:
                    # Parse and evaluate wait condition
                    condition = StepCondition(
                        variable=wait_condition.get("variable", ""),
                        operator=ConditionOperator(wait_condition.get("operator", "exists")),
                        value=wait_condition.get("value", True)
                    )

                    if await self._evaluate_condition(condition, execution):
                        break

                    await asyncio.sleep(0.5)  # Check every 500ms
                else:
                    # Timeout
                    logger.warning(f"Wait step {step.id} timed out after {max_wait_time}s")
            else:
                # Simple time-based wait
                await asyncio.sleep(wait_time)

            execution.step_results[step.id] = {
                "wait_time": wait_time,
                "actual_wait_time": time.time() - start_time
            }

            return True

        except Exception as e:
            logger.error(f"Wait step {step.id} failed: {e}")
            return False

    async def _execute_custom_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute custom step type"""

        try:
            # Custom steps can be extended by subclassing
            # For now, treat as a basic task
            return await self._execute_task_step(step, execution, workflow)

        except Exception as e:
            logger.error(f"Custom step {step.id} failed: {e}")
            return False

    async def _assign_agent_to_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Optional[str]:
        """Assign an agent to a step based on requirements"""

        # Check if agent is already assigned
        if step.assigned_agent:
            return step.assigned_agent

        # Check if agent was assigned by previous step
        if step.id in execution.agent_assignments:
            return execution.agent_assignments[step.id]

        # Use agent requirements to find suitable agent
        if step.agent_requirements:
            return await self._find_suitable_agent(step.agent_requirements, execution)

        # Fallback to any available agent
        return await self._get_any_available_agent(execution)

    async def _find_suitable_agent(
        self,
        requirements: AgentRequirement,
        execution: WorkflowExecution
    ) -> Optional[str]:
        """Find an agent that meets the requirements"""

        # Get available agents from agent team
        agent_team = execution.agent_team or {}
        available_agents = agent_team.get("agents", [])

        if not available_agents:
            logger.warning("No agents available in agent team")
            return None

        # Filter agents based on requirements
        suitable_agents = []

        for agent in available_agents:
            agent_id = agent.get("id", "")
            agent_capabilities = set(agent.get("capabilities", []))
            required_capabilities = set(requirements.capabilities)

            # Check capabilities
            if required_capabilities and not required_capabilities.issubset(agent_capabilities):
                continue

            # Check excluded agents
            if agent_id in requirements.excluded_agents:
                continue

            # Check agent load
            current_load = execution.agent_loads.get(agent_id, 0.0)
            if current_load > requirements.max_load:
                continue

            suitable_agents.append(agent)

        if not suitable_agents:
            logger.warning(f"No suitable agents found for requirements: {requirements}")
            return None

        # Select agent based on strategy
        selected_agent = await self._select_agent_by_strategy(
            suitable_agents,
            requirements.selection_strategy,
            execution
        )

        if selected_agent:
            agent_id = selected_agent.get("id", "")
            # Update agent load
            execution.agent_loads[agent_id] = execution.agent_loads.get(agent_id, 0.0) + 0.1
            return agent_id

        return None

    async def _select_agent_by_strategy(
        self,
        agents: List[Dict[str, Any]],
        strategy: AgentSelectionStrategy,
        execution: WorkflowExecution
    ) -> Optional[Dict[str, Any]]:
        """Select agent based on selection strategy"""

        if not agents:
            return None

        if strategy == AgentSelectionStrategy.ROUND_ROBIN:
            # Simple round-robin selection
            total_assignments = sum(1 for agent_id in execution.agent_assignments.values())
            return agents[total_assignments % len(agents)]

        elif strategy == AgentSelectionStrategy.LEAST_LOADED:
            # Select agent with lowest current load
            min_load = float('inf')
            selected_agent = None

            for agent in agents:
                agent_id = agent.get("id", "")
                load = execution.agent_loads.get(agent_id, 0.0)
                if load < min_load:
                    min_load = load
                    selected_agent = agent

            return selected_agent

        elif strategy == AgentSelectionStrategy.RANDOM:
            import random
            return random.choice(agents)

        elif strategy == AgentSelectionStrategy.PRIORITY_BASED:
            # Select agent with highest priority
            return max(agents, key=lambda a: a.get("priority", 0))

        else:  # CAPABILITY_MATCH or default
            # Select first suitable agent
            return agents[0]

    async def _get_any_available_agent(self, execution: WorkflowExecution) -> Optional[str]:
        """Get any available agent as fallback"""

        agent_team = execution.agent_team or {}
        available_agents = agent_team.get("agents", [])

        if available_agents:
            # Return least loaded agent
            min_load = float('inf')
            selected_agent_id = None

            for agent in available_agents:
                agent_id = agent.get("id", "")
                load = execution.agent_loads.get(agent_id, 0.0)
                if load < min_load:
                    min_load = load
                    selected_agent_id = agent_id

            return selected_agent_id

        return None

    async def _execute_step_with_agent(self, agent_id: str, step_context: Dict[str, Any]) -> Any:
        """Execute step through assigned agent"""

        try:
            # This would integrate with the actual agent execution system
            # For now, simulate agent execution

            logger.info(f"Executing step {step_context['step_id']} with agent {agent_id}")

            # Simulate processing time
            await asyncio.sleep(0.1)

            # Return mock success result
            return {
                "success": True,
                "agent_id": agent_id,
                "step_id": step_context["step_id"],
                "result": "Step executed successfully",
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Agent {agent_id} failed to execute step: {e}")
            return {
                "success": False,
                "agent_id": agent_id,
                "error": str(e),
                "timestamp": time.time()
            }

    async def _handle_step_outputs(self, step: WorkflowStep, execution: WorkflowExecution) -> None:
        """Handle step output variables"""

        if not step.output_variables:
            return

        step_result = execution.step_results.get(step.id, {})

        for var_name, result_key in step.output_variables.items():
            if result_key in step_result:
                value = step_result[result_key]
                execution.variables[var_name] = WorkflowVariable(
                    name=var_name,
                    value=value,
                    type=type(value).__name__,
                    description=f"Output from step {step.id}"
                )

    async def _drop_pheromone(self, signal_type: str, payload: Dict[str, Any], project_id: str) -> None:
        """Drop pheromone signal"""

        if self.pheromone_bus:
            try:
                # Use pheromone bus if available
                await self.pheromone_bus.drop_pheromone(
                    signal=signal_type,
                    payload=payload,
                    project_id=project_id
                )
            except Exception as e:
                logger.warning(f"Failed to drop pheromone: {e}")
        else:
            # Log pheromone for debugging
            logger.info(f"Pheromone: {signal_type} - {payload}")

    # Public API methods

    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get execution status by ID"""
        return self.active_executions.get(execution_id)

    def get_all_executions(self) -> List[WorkflowExecution]:
        """Get all active executions"""
        return list(self.active_executions.values())

    async def pause_execution(self, execution_id: str) -> bool:
        """Pause workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.RUNNING:
            execution.status = WorkflowStatus.PAUSED
            return True
        return False

    async def resume_execution(self, execution_id: str) -> bool:
        """Resume paused workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.PAUSED:
            execution.status = WorkflowStatus.RUNNING
            return True
        return False

    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status in [WorkflowStatus.RUNNING, WorkflowStatus.PAUSED]:
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at
            return True
        return False

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return {
            **self.execution_stats,
            "active_executions": len(self.active_executions),
            "agent_registry_size": len(self.agent_registry)
        }

# Global workflow engine instance
_global_engine: Optional[WorkflowExecutionEngine] = None

def get_workflow_engine() -> WorkflowExecutionEngine:
    """Get the global workflow engine instance"""
    global _global_engine
    if _global_engine is None:
        _global_engine = WorkflowExecutionEngine()
    return _global_engine

async def initialize_workflow_engine(pheromone_bus=None) -> WorkflowExecutionEngine:
    """Initialize the global workflow engine"""
    global _global_engine
    if _global_engine is None:
        _global_engine = WorkflowExecutionEngine(pheromone_bus)
    return _global_engine

# Convenience functions for backward compatibility

# Legacy WorkflowEngine is defined later in the file to maintain compatibility

# Legacy functions for backward compatibility

def load_workflow_definitions(workflows_dir: str = "workflows") -> Dict[str, WorkflowDefinition]:
    """Load all workflow definitions from directory"""
    workflows = {}
    workflows_path = Path(workflows_dir)

    if workflows_path.exists():
        parser = WorkflowYAMLParser()

        for yaml_file in workflows_path.glob("*.yaml"):
            try:
                workflow = parser.parse_workflow_file(yaml_file)
                workflows[workflow.id] = workflow
            except Exception as e:
                logger.warning(f"Failed to load workflow {yaml_file}: {e}")

        for yml_file in workflows_path.glob("*.yml"):
            try:
                workflow = parser.parse_workflow_file(yml_file)
                workflows[workflow.id] = workflow
            except Exception as e:
                logger.warning(f"Failed to load workflow {yml_file}: {e}")

    return workflows

def get_workflow_for_project_type(project_type: str) -> str:
    """Get appropriate workflow ID for project type"""
    workflow_mapping = {
        "fullstack": "greenfield-fullstack",
        "frontend": "greenfield-fullstack",
        "backend": "greenfield-fullstack",
        "mobile": "greenfield-fullstack",
        "api": "greenfield-fullstack",
        "enhancement": "enhancement",
        "bugfix": "debugging",
        "testing": "testing"
    }

    return workflow_mapping.get(project_type, "greenfield-fullstack")

async def execute_bmad_workflow(
    workflow_type: str,
    project_context: Dict[str, Any],
    agent_team: Dict[str, Any],
    pheromone_bus: Dict[str, Any]
) -> WorkflowExecution:
    """Execute BMAD workflow with full context"""

    engine = get_workflow_engine()

    return await engine.start_workflow(
        workflow_id=workflow_type,
        project_id=project_context.get("project_id", "unknown"),
        agent_team=agent_team,
        pheromone_bus=pheromone_bus,
        context=project_context
    )

# Export main classes and functions
__all__ = [
    # Core classes
    'WorkflowDefinition',
    'WorkflowExecution',
    'WorkflowStep',
    'WorkflowVariable',
    'WorkflowYAMLParser',
    'WorkflowExecutionEngine',

    # Enums
    'WorkflowStatus',
    'StepStatus',
    'StepType',
    'ConditionOperator',
    'AgentSelectionStrategy',

    # Data classes
    'StepCondition',
    'AgentRequirement',
    'RetryConfig',
    'TimeoutConfig',

    # Exceptions
    'WorkflowValidationError',
    'WorkflowExecutionError',
    'WorkflowTimeoutError',
    'AgentAssignmentError',

    # Global functions
    'get_workflow_engine',
    'initialize_workflow_engine',
    'load_workflow_definitions',
    'get_workflow_for_project_type',
    'execute_bmad_workflow',

    # Legacy compatibility
    'WorkflowEngine',
    'WorkflowPhase',  # Legacy
    'PhaseStatus'     # Legacy
]

class WorkflowEngine:
    """BMAD Workflow Engine for orchestrating multi-phase project generation"""
    
    def __init__(self):
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.bmad_path = Path("BMAD-METHOD-main/BMAD-METHOD-main")
        self._load_bmad_workflows()
    
    def _load_bmad_workflows(self):
        """Load BMAD workflow definitions from YAML files"""
        try:
            workflows_dir = self.bmad_path / "bmad-core" / "workflows"
            if not workflows_dir.exists():
                logger.warning(f"BMAD workflows directory not found: {workflows_dir}")
                self._create_default_workflows()
                return
            
            for workflow_file in workflows_dir.glob("*.yml"):
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        workflow_data = yaml.safe_load(f)
                    
                    workflow_def = self._parse_workflow_yaml(workflow_data, workflow_file.stem)
                    self.workflows[workflow_def.id] = workflow_def
                    logger.info(f"Loaded workflow: {workflow_def.name}")
                    
                except Exception as e:
                    logger.error(f"Failed to load workflow {workflow_file}: {e}")
            
            # Load expansion pack workflows
            expansion_dir = self.bmad_path / "expansion-packs"
            if expansion_dir.exists():
                for pack_dir in expansion_dir.iterdir():
                    if pack_dir.is_dir():
                        pack_workflows = pack_dir / "workflows"
                        if pack_workflows.exists():
                            for workflow_file in pack_workflows.glob("*.yml"):
                                try:
                                    with open(workflow_file, 'r', encoding='utf-8') as f:
                                        workflow_data = yaml.safe_load(f)
                                    
                                    workflow_def = self._parse_workflow_yaml(workflow_data, workflow_file.stem)
                                    self.workflows[workflow_def.id] = workflow_def
                                    logger.info(f"Loaded expansion workflow: {workflow_def.name}")
                                    
                                except Exception as e:
                                    logger.error(f"Failed to load expansion workflow {workflow_file}: {e}")
            
            logger.info(f"Loaded {len(self.workflows)} BMAD workflows")
            
        except Exception as e:
            logger.error(f"Failed to load BMAD workflows: {e}")
            self._create_default_workflows()
    
    def _parse_workflow_yaml(self, data: Dict[str, Any], file_id: str) -> WorkflowDefinition:
        """Parse YAML workflow data into WorkflowDefinition"""
        
        workflow_info = data.get('workflow', {})
        
        # Parse phases from sequence
        phases = []
        sequence = workflow_info.get('sequence', [])
        
        for i, step in enumerate(sequence):
            if isinstance(step, dict):
                # Handle different step formats
                if 'agent' in step:
                    phase = WorkflowPhase(
                        id=f"phase_{i+1}",
                        name=step.get('name', f"Phase {i+1}"),
                        description=step.get('notes', ''),
                        agent=step['agent'],
                        creates=step.get('creates', []) if isinstance(step.get('creates'), list) else [step.get('creates', '')],
                        requires=step.get('requires', []) if isinstance(step.get('requires'), list) else [step.get('requires', '')] if step.get('requires') else [],
                        optional_steps=step.get('optional_steps', []),
                        duration_minutes=self._parse_duration(step.get('duration', '30 minutes')),
                        notes=step.get('notes', ''),
                        condition=step.get('condition')
                    )
                elif 'step' in step:
                    # Handle step-based format (like game workflows)
                    phase = WorkflowPhase(
                        id=step.get('step', f"phase_{i+1}"),
                        name=step.get('step', f"Phase {i+1}").replace('_', ' ').title(),
                        description=step.get('notes', ''),
                        agent=step.get('agent', 'developer'),
                        creates=step.get('creates', []) if isinstance(step.get('creates'), list) else [step.get('creates', '')],
                        requires=step.get('requires', []) if isinstance(step.get('requires'), list) else [step.get('requires', '')] if step.get('requires') else [],
                        optional_steps=step.get('optional_steps', []),
                        duration_minutes=self._parse_duration(step.get('duration', '30 minutes')),
                        notes=step.get('notes', '')
                    )
                else:
                    continue
                
                phases.append(phase)
        
        # Calculate total duration
        total_duration = sum(phase.duration_minutes for phase in phases)
        
        return WorkflowDefinition(
            id=workflow_info.get('id', file_id),
            name=workflow_info.get('name', file_id.replace('-', ' ').title()),
            version=workflow_info.get('version', '1.0.0'),  # Add default version
            description=workflow_info.get('description', ''),
            type=workflow_info.get('type', 'greenfield'),
            project_types=workflow_info.get('project_types', []),
            phases=phases,
            total_duration_minutes=total_duration,
            flow_diagram=workflow_info.get('flow_diagram', ''),
            metadata=workflow_info
        )
    
    def _parse_duration(self, duration_str: str) -> int:
        """Parse duration string to minutes"""
        if isinstance(duration_str, int):
            return duration_str
        
        duration_str = str(duration_str).lower()
        
        if 'hour' in duration_str:
            try:
                hours = float(duration_str.split()[0])
                return int(hours * 60)
            except:
                return 60
        elif 'minute' in duration_str:
            try:
                minutes = float(duration_str.split()[0].split('-')[0])
                return int(minutes)
            except:
                return 30
        else:
            return 30
    
    def _create_default_workflows(self):
        """Create default workflows when BMAD files are not available"""
        
        # Greenfield Full Stack Workflow
        fullstack_phases = [
            WorkflowPhase("requirements_analysis", "Requirements Analysis", "Analyze requirements and create user stories", "analyst", ["project-brief.md"], [], [], 30),
            WorkflowPhase("product_planning", "Product Planning", "Create comprehensive PRD", "pm", ["prd.md"], ["project-brief.md"], [], 45),
            WorkflowPhase("ux_design", "UX Design", "Create UI/UX specifications", "ux-expert", ["front-end-spec.md"], ["prd.md"], ["user_research_prompt"], 60),
            WorkflowPhase("architecture_design", "Architecture Design", "Design system architecture", "architect", ["fullstack-architecture.md"], ["prd.md", "front-end-spec.md"], ["technical_research_prompt"], 45),
            WorkflowPhase("development", "Development", "Implement the application", "developer", ["source_code"], ["fullstack-architecture.md"], [], 120),
            WorkflowPhase("testing", "Testing & QA", "Test and validate implementation", "qa", ["test_results"], ["source_code"], [], 45),
            WorkflowPhase("documentation", "Documentation", "Create final documentation", "analyst", ["documentation"], ["source_code"], [], 30)
        ]
        
        self.workflows["greenfield-fullstack"] = WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Full Stack Development",
            description="Complete full-stack application development from concept to deployment",
            type="greenfield",
            project_types=["fullstack", "web-app"],
            phases=fullstack_phases,
            total_duration_minutes=sum(p.duration_minutes for p in fullstack_phases)
        )
        
        # Frontend-only workflow
        frontend_phases = [
            WorkflowPhase("requirements_analysis", "Requirements Analysis", "Analyze UI requirements", "analyst", ["project-brief.md"], [], [], 20),
            WorkflowPhase("ux_design", "UX Design", "Create UI/UX specifications", "ux-expert", ["front-end-spec.md"], ["project-brief.md"], [], 45),
            WorkflowPhase("frontend_development", "Frontend Development", "Implement frontend application", "frontend-developer", ["frontend_code"], ["front-end-spec.md"], [], 90),
            WorkflowPhase("testing", "Testing", "Test frontend functionality", "qa", ["test_results"], ["frontend_code"], [], 30)
        ]
        
        self.workflows["greenfield-frontend"] = WorkflowDefinition(
            id="greenfield-frontend",
            name="Greenfield Frontend Development",
            description="Frontend-only application development",
            type="greenfield",
            project_types=["frontend", "spa"],
            phases=frontend_phases,
            total_duration_minutes=sum(p.duration_minutes for p in frontend_phases)
        )
        
        logger.info("Created default workflows")
    
    async def start_workflow(self, workflow_id: str, project_id: str, agent_team: Dict[str, Any], 
                           pheromone_bus: Dict[str, Any], context: Dict[str, Any]) -> WorkflowExecution:
        """Start a new workflow execution"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        execution = WorkflowExecution(
            workflow_id=workflow_id,
            project_id=project_id,
            status=WorkflowStatus.IN_PROGRESS,
            start_time=datetime.now(),
            agent_team=agent_team,
            pheromone_bus=pheromone_bus
        )
        
        self.executions[project_id] = execution
        
        # Drop pheromone for workflow start
        await self._drop_pheromone("workflow_started", {
            "workflow_id": workflow_id,
            "project_id": project_id,
            "total_phases": len(self.workflows[workflow_id].phases)
        }, project_id)
        
        logger.info(f"Started workflow {workflow_id} for project {project_id}")
        
        # Start execution in background
        asyncio.create_task(self._execute_workflow(execution, context))
        
        return execution
    
    async def _execute_workflow(self, execution: WorkflowExecution, context: Dict[str, Any]):
        """Execute workflow phases sequentially"""
        
        workflow = self.workflows[execution.workflow_id]
        
        try:
            for i, phase in enumerate(workflow.phases):
                execution.current_phase_index = i
                execution.progress = i / len(workflow.phases)
                
                # Check if phase should be skipped based on condition
                if phase.condition and not self._evaluate_condition(phase.condition, execution.phase_results):
                    phase.status = PhaseStatus.SKIPPED
                    logger.info(f"Skipping phase {phase.name} due to condition: {phase.condition}")
                    continue
                
                # Execute phase
                phase.status = PhaseStatus.IN_PROGRESS
                phase.start_time = datetime.now()
                
                await self._drop_pheromone("phase_started", {
                    "phase_id": phase.id,
                    "phase_name": phase.name,
                    "agent": phase.agent,
                    "estimated_duration": phase.duration_minutes
                }, execution.project_id)
                
                try:
                    phase_result = await self._execute_phase(phase, execution, context)
                    execution.phase_results[phase.id] = phase_result
                    
                    phase.status = PhaseStatus.COMPLETED
                    phase.end_time = datetime.now()
                    phase.outputs = phase_result.get("outputs", [])
                    
                    await self._drop_pheromone("phase_completed", {
                        "phase_id": phase.id,
                        "phase_name": phase.name,
                        "success": phase_result.get("success", False),
                        "outputs": phase.outputs,
                        "duration_minutes": (phase.end_time - phase.start_time).total_seconds() / 60
                    }, execution.project_id)
                    
                except Exception as e:
                    phase.status = PhaseStatus.FAILED
                    phase.end_time = datetime.now()
                    execution.error_message = str(e)
                    
                    await self._drop_pheromone("phase_failed", {
                        "phase_id": phase.id,
                        "phase_name": phase.name,
                        "error": str(e)
                    }, execution.project_id)
                    
                    logger.error(f"Phase {phase.name} failed: {e}")
                    break
            
            # Complete workflow
            execution.status = WorkflowStatus.COMPLETED
            execution.end_time = datetime.now()
            execution.progress = 1.0
            
            await self._drop_pheromone("workflow_completed", {
                "workflow_id": execution.workflow_id,
                "project_id": execution.project_id,
                "total_duration_minutes": (execution.end_time - execution.start_time).total_seconds() / 60,
                "phases_completed": sum(1 for phase in workflow.phases if phase.status == PhaseStatus.COMPLETED)
            }, execution.project_id)
            
            logger.info(f"Workflow {execution.workflow_id} completed for project {execution.project_id}")
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.end_time = datetime.now()
            execution.error_message = str(e)
            
            await self._drop_pheromone("workflow_failed", {
                "workflow_id": execution.workflow_id,
                "project_id": execution.project_id,
                "error": str(e)
            }, execution.project_id)
            
            logger.error(f"Workflow {execution.workflow_id} failed: {e}")
    
    async def _execute_phase(self, phase: WorkflowPhase, execution: WorkflowExecution, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow phase"""
        
        # Import agent execution here to avoid circular imports
        try:
            from .agent_executors import create_agent_executor
            
            # Create agent executor for this phase
            agent_executor = create_agent_executor(phase.agent)
            
            # Prepare phase context
            phase_context = {
                **context,
                "phase": phase.id,
                "phase_name": phase.name,
                "creates": phase.creates,
                "requires": phase.requires,
                "optional_steps": phase.optional_steps,
                "previous_results": execution.phase_results
            }
            
            # Execute the phase
            result = await agent_executor.execute(phase_context)
            
            return result
            
        except ImportError:
            # Fallback to mock execution
            logger.warning(f"Agent executors not available, using mock execution for phase {phase.name}")
            
            await asyncio.sleep(1)  # Simulate work
            
            return {
                "success": True,
                "outputs": phase.creates,
                "message": f"Mock execution of {phase.name} completed",
                "files_created": len(phase.creates)
            }
    
    def _evaluate_condition(self, condition: str, phase_results: Dict[str, Any]) -> bool:
        """Evaluate a phase condition"""
        # Simple condition evaluation - can be extended
        if condition == "architecture_suggests_prd_changes":
            arch_result = phase_results.get("architecture_design", {})
            return arch_result.get("suggests_prd_changes", False)
        
        return True  # Default to true for unknown conditions
    
    async def _drop_pheromone(self, signal: str, payload: Dict[str, Any], project_id: str):
        """Drop a pheromone signal"""
        try:
            from .pheromone_system import get_pheromone_system
            pheromone_system = get_pheromone_system()
            await pheromone_system.drop_pheromone(signal, payload, project_id)
        except Exception as e:
            logger.warning(f"Failed to drop pheromone: {e}")
    
    def get_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get workflow definition by ID"""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[WorkflowDefinition]:
        """List all available workflows"""
        return list(self.workflows.values())
    
    def get_execution(self, project_id: str) -> Optional[WorkflowExecution]:
        """Get workflow execution by project ID"""
        return self.executions.get(project_id)
    
    def get_workflows_for_project_type(self, project_type: str) -> List[WorkflowDefinition]:
        """Get workflows suitable for a project type"""
        return [w for w in self.workflows.values() if project_type in w.project_types or not w.project_types]

    def get_workflow_types(self) -> List[str]:
        """Get available workflow types"""
        return [
            "greenfield-fullstack",
            "enhancement",
            "debugging",
            "testing"
        ]

# Global workflow engine instance
_workflow_engine = None

def get_workflow_engine() -> WorkflowEngine:
    """Get the global workflow engine instance"""
    global _workflow_engine
    if _workflow_engine is None:
        _workflow_engine = WorkflowEngine()
    return _workflow_engine
