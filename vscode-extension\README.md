# Aetherforge VS Code Extension

Autonomous AI Software Creation System for VS Code.

## Features

- Create complete software projects from natural language descriptions
- Real-time project generation monitoring
- Multi-agent AI coordination
- BMAD workflow integration

## Usage

1. Open Command Palette (`Ctrl+Shift+P`)
2. Run "Start Aetherforge"
3. Describe your project and let AI create it!

## Requirements

- Aetherforge orchestrator running on localhost:8000
- Node.js and npm installed

## Configuration

Set the orchestrator URL in VS Code settings:
- `aetherforge.orchestratorUrl`: URL of the Aetherforge service

## Getting Started

1. Start the Aetherforge orchestrator:
   ```bash
   python src/orchestrator.py
   ```

2. Open VS Code and run the "Start Aetherforge" command

3. Use the interface to create amazing software projects!

## Support

For issues and documentation, visit the Aetherforge project repository.
