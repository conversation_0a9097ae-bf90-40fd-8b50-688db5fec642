#!/usr/bin/env python3
"""
Test Specific Issues
Tests the specific failing components to identify the exact problems
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_logging_system():
    """Test the logging system specifically"""
    print("🔍 Testing Logging System...")
    
    try:
        # Test 1: Import logging functions
        from orchestrator import setup_enhanced_logging, setup_project_logging
        print("   ✅ Logging functions imported successfully")
        
        # Test 2: Setup enhanced logging
        try:
            main_logger = setup_enhanced_logging()
            print(f"   ✅ Enhanced logging setup successful")
            print(f"      Logger: {main_logger.name}")
            print(f"      Handlers: {len(main_logger.handlers)}")
        except Exception as e:
            print(f"   ❌ Enhanced logging setup failed: {e}")
            return False
        
        # Test 3: Setup project logging
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                project_path = Path(temp_dir) / "test_project"
                project_path.mkdir()
                
                project_logger = setup_project_logging("test_123", project_path)
                print(f"   ✅ Project logging setup successful")
                print(f"      Project logger: {project_logger.name}")
                
                # Test logging
                project_logger.info("Test message")
                
                # Check log file
                log_file = project_path / "logs" / "project.log"
                if log_file.exists():
                    print(f"   ✅ Log file created: {log_file}")
                else:
                    print(f"   ⚠️  Log file not found: {log_file}")
                
        except Exception as e:
            print(f"   ❌ Project logging setup failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Logging system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_agents():
    """Test the enhanced agent generation specifically"""
    print("🔍 Testing Enhanced Agent Generation...")
    
    try:
        # Test 1: Import required classes
        from orchestrator import (
            generate_enhanced_agent_team, 
            ProjectRequest, 
            ProjectType, 
            AgentBehavior,
            setup_project_logging
        )
        print("   ✅ Agent classes imported successfully")
        
        # Test 2: Create test request
        try:
            test_request = ProjectRequest(
                prompt="Create a simple web application for task management",
                project_type=ProjectType.FULLSTACK,
                agent_behavior=AgentBehavior.BALANCED
            )
            print("   ✅ Test request created successfully")
        except Exception as e:
            print(f"   ❌ Test request creation failed: {e}")
            return False
        
        # Test 3: Setup project logging for agent test
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                project_path = Path(temp_dir) / "test_project"
                project_path.mkdir()
                
                project_logger = setup_project_logging("test_456", project_path)
                print("   ✅ Project logger setup for agent test")
                
                # Test 4: Generate agent team
                try:
                    agent_team = await generate_enhanced_agent_team(
                        test_request, 
                        "test_workflow", 
                        "test_456", 
                        project_logger
                    )
                    
                    print(f"   ✅ Agent team generated successfully")
                    print(f"      Team ID: {agent_team.get('team_id', 'N/A')}")
                    print(f"      Agents: {len(agent_team.get('agents', []))}")
                    
                    # Check agent details
                    for i, agent in enumerate(agent_team.get('agents', [])[:3]):  # Show first 3
                        print(f"      Agent {i+1}: {agent.get('role', 'unknown')} - {agent.get('name', 'unnamed')}")
                    
                    return True
                    
                except Exception as e:
                    print(f"   ❌ Agent team generation failed: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
                
        except Exception as e:
            print(f"   ❌ Project setup for agent test failed: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ Enhanced agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_configuration():
    """Test the configuration system"""
    print("🔍 Testing Configuration System...")
    
    try:
        # Test configuration import
        from orchestrator import OrchestratorConfig, ProjectType, AgentBehavior, Priority
        print("   ✅ Configuration classes imported successfully")
        
        # Test configuration creation
        config = OrchestratorConfig()
        print(f"   ✅ Configuration created successfully")
        print(f"      Projects dir: {config.projects_dir}")
        print(f"      Max concurrent: {config.max_concurrent_projects}")
        
        # Test enums
        print(f"   ✅ ProjectType enum: {len(list(ProjectType))} types")
        print(f"   ✅ AgentBehavior enum: {len(list(AgentBehavior))} behaviors")
        print(f"   ✅ Priority enum: {len(list(Priority))} levels")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🔧 Testing Specific Issues in Enhanced Orchestrator")
    print("=" * 60)
    
    results = {}
    
    # Test configuration first (foundation)
    results["configuration"] = await test_configuration()
    
    # Test logging system
    results["logging"] = await test_logging_system()
    
    # Test enhanced agents
    results["agents"] = await test_enhanced_agents()
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {test_name.title()}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\nOverall: {passed_count}/{total_count} tests passed ({(passed_count/total_count)*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 All specific issues resolved!")
    else:
        print("🔧 Some issues still need fixing.")

if __name__ == "__main__":
    asyncio.run(main())
