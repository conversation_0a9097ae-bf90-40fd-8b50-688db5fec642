#!/bin/bash

# Aetherforge Management Script
# Provides easy commands to manage the Aetherforge system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${AETHERFORGE_ENV:-development}
COMPOSE_FILE="docker-compose.yml"

if [ "$ENVIRONMENT" = "production" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
fi

# Function to print status
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Help function
show_help() {
    echo -e "${BLUE}🔧 Aetherforge Management Script${NC}"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  status          Show service status"
    echo "  logs [service]  Show logs for all services or specific service"
    echo "  build           Build all Docker images"
    echo "  clean           Clean up containers, images, and volumes"
    echo "  health          Check health of all services"
    echo "  create-project  Create a test project"
    echo "  backup          Backup data volumes"
    echo "  restore         Restore data from backup"
    echo "  update          Update and rebuild services"
    echo "  shell [service] Open shell in service container"
    echo "  help            Show this help message"
    echo ""
    echo "Environment: $ENVIRONMENT (set AETHERFORGE_ENV to change)"
    echo "Compose file: $COMPOSE_FILE"
}

# Start services
start_services() {
    print_info "Starting Aetherforge services..."
    docker-compose -f $COMPOSE_FILE up -d
    print_status "Services started"
    show_status
}

# Stop services
stop_services() {
    print_info "Stopping Aetherforge services..."
    docker-compose -f $COMPOSE_FILE down
    print_status "Services stopped"
}

# Restart services
restart_services() {
    print_info "Restarting Aetherforge services..."
    docker-compose -f $COMPOSE_FILE restart
    print_status "Services restarted"
    show_status
}

# Show service status
show_status() {
    print_info "Service Status:"
    docker-compose -f $COMPOSE_FILE ps
}

# Show logs
show_logs() {
    local service=$1
    if [ -n "$service" ]; then
        print_info "Showing logs for $service..."
        docker-compose -f $COMPOSE_FILE logs -f "$service"
    else
        print_info "Showing logs for all services..."
        docker-compose -f $COMPOSE_FILE logs -f
    fi
}

# Build images
build_images() {
    print_info "Building Docker images..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    print_status "Images built"
}

# Clean up
clean_up() {
    print_warning "This will remove all containers, images, and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_info "Cleaning up..."
        docker-compose -f $COMPOSE_FILE down -v --remove-orphans
        docker system prune -af --volumes
        print_status "Cleanup completed"
    else
        print_info "Cleanup cancelled"
    fi
}

# Health check
health_check() {
    print_info "Checking service health..."
    
    services=(
        "orchestrator:8000/health"
        "archon:8100/health"
        "mcp-crawl4ai:8051/health"
        "pheromind:8502/health"
        "bmad:8503/health"
    )
    
    for service_url in "${services[@]}"; do
        service=$(echo $service_url | cut -d: -f1)
        url="http://localhost:$(echo $service_url | cut -d: -f2-)"
        
        if curl -f -s "$url" > /dev/null 2>&1; then
            print_status "$service is healthy"
        else
            print_error "$service is not responding"
        fi
    done
}

# Create test project
create_test_project() {
    print_info "Creating test project..."
    
    response=$(curl -s -X POST "http://localhost:8000/projects" \
        -H "Content-Type: application/json" \
        -d '{
            "prompt": "Create a simple calculator app with basic arithmetic operations",
            "project_name": "CalculatorApp",
            "project_type": "fullstack"
        }')
    
    if echo "$response" | grep -q "success"; then
        print_status "Test project created successfully"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
    else
        print_error "Failed to create test project"
        echo "$response"
    fi
}

# Backup data
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    print_info "Creating backup in $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # Backup volumes
    docker run --rm -v aetherforge_postgres-data:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/postgres-data.tar.gz -C /data .
    docker run --rm -v aetherforge_redis-data:/data -v "$(pwd)/$backup_dir":/backup alpine tar czf /backup/redis-data.tar.gz -C /data .
    
    # Backup projects
    if [ -d "projects" ]; then
        tar czf "$backup_dir/projects.tar.gz" projects/
    fi
    
    print_status "Backup created in $backup_dir"
}

# Restore data
restore_data() {
    local backup_dir=$1
    if [ -z "$backup_dir" ]; then
        print_error "Please specify backup directory"
        exit 1
    fi
    
    if [ ! -d "$backup_dir" ]; then
        print_error "Backup directory $backup_dir not found"
        exit 1
    fi
    
    print_warning "This will overwrite existing data. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_info "Restoring from $backup_dir..."
        
        # Stop services
        docker-compose -f $COMPOSE_FILE down
        
        # Restore volumes
        if [ -f "$backup_dir/postgres-data.tar.gz" ]; then
            docker run --rm -v aetherforge_postgres-data:/data -v "$(pwd)/$backup_dir":/backup alpine tar xzf /backup/postgres-data.tar.gz -C /data
        fi
        
        if [ -f "$backup_dir/redis-data.tar.gz" ]; then
            docker run --rm -v aetherforge_redis-data:/data -v "$(pwd)/$backup_dir":/backup alpine tar xzf /backup/redis-data.tar.gz -C /data
        fi
        
        # Restore projects
        if [ -f "$backup_dir/projects.tar.gz" ]; then
            tar xzf "$backup_dir/projects.tar.gz"
        fi
        
        print_status "Restore completed"
        print_info "Starting services..."
        docker-compose -f $COMPOSE_FILE up -d
    else
        print_info "Restore cancelled"
    fi
}

# Update services
update_services() {
    print_info "Updating Aetherforge services..."
    
    # Pull latest images
    docker-compose -f $COMPOSE_FILE pull
    
    # Rebuild custom images
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    # Restart services
    docker-compose -f $COMPOSE_FILE up -d
    
    print_status "Services updated"
}

# Open shell in service
open_shell() {
    local service=$1
    if [ -z "$service" ]; then
        print_error "Please specify service name"
        exit 1
    fi
    
    print_info "Opening shell in $service..."
    docker-compose -f $COMPOSE_FILE exec "$service" /bin/bash || \
    docker-compose -f $COMPOSE_FILE exec "$service" /bin/sh
}

# Main command handling
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    build)
        build_images
        ;;
    clean)
        clean_up
        ;;
    health)
        health_check
        ;;
    create-project)
        create_test_project
        ;;
    backup)
        backup_data
        ;;
    restore)
        restore_data "$2"
        ;;
    update)
        update_services
        ;;
    shell)
        open_shell "$2"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
