# Aetherforge User Guide

## 🎯 Getting Started

Welcome to Aetherforge! This guide will help you create your first autonomous software project and master the system's capabilities.

## 🚀 Creating Your First Project

### Step 1: Open Aetherforge

1. Start VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Aetherforge: Start"
4. Select the command to open the Aetherforge panel

### Step 2: Describe Your Project

In the "Create Project" tab, enter a detailed description of what you want to build:

**Good Examples:**
```
Create a todo list application with user authentication, 
real-time updates, and the ability to share lists with other users. 
Include a modern React frontend and Express.js backend with PostgreSQL database.
```

```
Build a personal finance tracker that allows users to connect bank accounts, 
categorize transactions, set budgets, and view spending analytics. 
Use React for the frontend and Python FastAPI for the backend.
```

**Tips for Better Results:**
- Be specific about features and functionality
- Mention preferred technologies if you have them
- Include user interface requirements
- Specify data storage needs
- Mention any integrations or APIs

### Step 3: Configure Project Settings

- **Project Name**: Optional, will be auto-generated if not provided
- **Project Type**: Choose from:
  - Full Stack Web Application (React + Express.js)
  - Frontend Application (React only)
  - Backend API Service (Express.js/FastAPI)
  - Mobile Application (React Native)
  - Desktop Application (Electron)

### Step 4: Advanced Options

- **Workflow**: Usually auto-selected, but you can choose specific workflows
- **Web Research**: Enable to include latest best practices and documentation
- **Agent Coordination**: Enable for advanced multi-agent collaboration

### Step 5: Generate Project

Click "Create Project" and watch the magic happen! The system will:

1. **Analyze Requirements** (30-60 seconds)
2. **Design Architecture** (1-2 minutes)
3. **Generate Code** (2-4 minutes)
4. **Create Tests** (1-2 minutes)
5. **Generate Documentation** (30-60 seconds)

## 📊 Understanding the Generation Process

### Phase 1: Requirements Analysis
- AI analyst breaks down your description
- Creates detailed user stories
- Identifies technical requirements
- Plans feature scope

### Phase 2: System Architecture
- AI architect designs system structure
- Selects appropriate technologies
- Plans database schema
- Creates component architecture

### Phase 3: Development Planning
- Creates detailed development roadmap
- Plans project structure
- Defines coding standards
- Sets up development environment

### Phase 4: Core Development
- AI developer writes frontend code
- Implements backend APIs
- Sets up database connections
- Creates user interfaces

### Phase 5: Testing & Validation
- AI QA engineer creates test suites
- Implements unit and integration tests
- Validates functionality
- Ensures code quality

### Phase 6: Documentation
- Generates comprehensive README
- Creates API documentation
- Writes deployment guides
- Adds code comments

## 🎛️ Using the System Status Tab

### Component Health Monitoring

The Status tab shows the health of all Aetherforge components:

- **🟢 Green**: Component is running and healthy
- **🟡 Yellow**: Component is reachable but may have issues
- **🔴 Red**: Component is offline or unreachable

### Key Components

1. **Orchestrator**: Main coordination service
2. **Archon**: Agent team generation
3. **Pheromind**: Agent coordination and visualization
4. **MCP-Crawl4AI**: Web research and documentation
5. **BMAD**: Development methodology framework

### Troubleshooting Components

If components show as offline:
1. They're optional - Aetherforge works with fallbacks
2. Check if external services are running
3. Verify network connectivity
4. Use "Initialize Components" button to restart

## 📁 Managing Projects

### Projects Tab Features

- **View All Projects**: See all generated projects
- **Project Details**: Click to view project information
- **Open Project**: Open project in new VS Code window
- **Refresh**: Update project list

### Project Structure

Each generated project includes:

```
my-project/
├── src/                    # Source code
│   ├── components/         # React components
│   ├── pages/             # Application pages
│   ├── services/          # API services
│   └── utils/             # Utility functions
├── server/                # Backend code
│   ├── routes/            # API routes
│   ├── models/            # Data models
│   └── middleware/        # Express middleware
├── tests/                 # Test files
├── docs/                  # Documentation
├── package.json           # Dependencies
├── Dockerfile            # Container configuration
├── docker-compose.yml    # Multi-service setup
├── .env.example          # Environment template
└── README.md             # Project documentation
```

### Running Generated Projects

1. **Navigate to project directory:**
   ```bash
   cd projects/my-project
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Build for production:**
   ```bash
   npm run build
   npm start
   ```

## ⚙️ Settings and Configuration

### Orchestrator Settings

- **URL**: Where the Aetherforge orchestrator is running
- **Default**: `http://localhost:8000`
- **Change**: If running on different host/port

### Project Settings

- **Projects Directory**: Where projects are saved
- **Default**: `./projects`
- **Recommendation**: Use absolute path for clarity

### Advanced Settings

Access via VS Code settings (`Ctrl+,`):

```json
{
  "aetherforge.orchestratorUrl": "http://localhost:8000",
  "aetherforge.projectsPath": "./projects",
  "aetherforge.autoRefresh": true,
  "aetherforge.enableNotifications": true,
  "aetherforge.debugMode": false,
  "aetherforge.maxConcurrentProjects": 3,
  "aetherforge.defaultProjectType": "fullstack"
}
```

## 🔍 Real-time Monitoring

### Project Generation Progress

Watch real-time progress through:

1. **Status Messages**: Live updates in the panel
2. **Pheromone Activity**: Agent communication tracking
3. **File Creation**: See files being generated
4. **Phase Completion**: Track workflow progress

### Notifications

Aetherforge will notify you when:
- Project generation starts
- Each phase completes
- Project generation finishes
- Errors occur

## 💡 Best Practices

### Writing Effective Prompts

**Do:**
- Be specific about functionality
- Mention user types and use cases
- Include technical preferences
- Specify data requirements
- Mention integrations needed

**Don't:**
- Be too vague ("make a website")
- Use overly technical jargon
- Request impossible features
- Forget about user experience
- Ignore scalability needs

### Project Types Guide

#### Full Stack Applications
Best for: Complete web applications with frontend and backend
Includes: React frontend, Express.js backend, database, authentication

#### Frontend Applications  
Best for: Client-side applications, SPAs, static sites
Includes: React components, routing, state management, styling

#### Backend APIs
Best for: Microservices, API-only applications, mobile backends
Includes: REST APIs, database models, authentication, documentation

#### Mobile Applications
Best for: Cross-platform mobile apps
Includes: React Native components, navigation, platform-specific features

### Performance Tips

1. **Clear Descriptions**: Reduce generation time with specific requirements
2. **Appropriate Scope**: Don't request overly complex applications
3. **Stable Internet**: Ensure good connection for AI API calls
4. **Resource Management**: Close unnecessary applications during generation

## 🐛 Troubleshooting

### Common Issues

#### "Project Generation Failed"
- Check internet connection
- Verify OpenAI API key is valid
- Ensure sufficient API quota
- Try simpler project description

#### "Orchestrator Not Responding"
- Check if orchestrator is running: `python src/orchestrator.py`
- Verify URL in settings
- Check firewall settings
- Restart orchestrator service

#### "VS Code Extension Not Working"
- Reload VS Code window: `Ctrl+Shift+P` → "Developer: Reload Window"
- Check extension is enabled
- Update to latest version
- Check developer console for errors

#### "Generated Project Won't Run"
- Install dependencies: `npm install`
- Check Node.js version (18+)
- Verify environment variables
- Check project README for specific instructions

### Getting Help

1. **Check Logs**: Look in VS Code developer console
2. **System Status**: Use the Status tab to check components
3. **Test Connection**: Use "Test Connection" in Settings
4. **Documentation**: Refer to technical documentation
5. **Support**: Contact support with error details

## 🎓 Advanced Usage

### Custom Workflows

You can specify custom workflows for specialized project types:
- `greenfield-fullstack`: Complete web applications
- `greenfield-frontend`: Frontend-only projects
- `greenfield-service`: Backend services and APIs
- `greenfield-mobile`: Mobile applications

### Integration with External Tools

Generated projects work with:
- **Git**: Automatic `.gitignore` and repository setup
- **Docker**: Container configuration included
- **CI/CD**: GitHub Actions workflows
- **Testing**: Jest, Cypress, and other testing frameworks
- **Deployment**: Vercel, Netlify, Heroku configurations

### Customizing Generated Code

After generation, you can:
1. Modify code as needed
2. Add additional features
3. Customize styling and UI
4. Integrate with external services
5. Deploy to your preferred platform

## 📈 Tips for Success

1. **Start Simple**: Begin with basic projects to understand the system
2. **Iterate**: Generate multiple versions with different approaches
3. **Learn**: Study generated code to understand patterns
4. **Customize**: Modify generated projects to fit your exact needs
5. **Share**: Use generated projects as starting points for larger applications

---

**Happy Creating!** 🎉 You're now ready to build amazing software with Aetherforge's autonomous AI system.
