"""
Component Adapters for Aetherforge
Standardized interfaces for all Aetherforge components
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
import os

logger = logging.getLogger(__name__)

class ComponentAdapter:
    """Base class for all component adapters"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url
        self.timeout = timeout
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if the component is healthy"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    return {"status": "healthy", "component": self.__class__.__name__}
                else:
                    return {"status": "unhealthy", "component": self.__class__.__name__, "error": f"HTTP {response.status}"}
        except Exception as e:
            return {"status": "unreachable", "component": self.__class__.__name__, "error": str(e)}


class ArchonAdapter(ComponentAdapter):
    """Adapter for Archon agent generation service"""
    
    def __init__(self):
        super().__init__(os.getenv("ARCHON_URL", "http://localhost:8100"))
    
    async def generate_agent_team(self, prompt: str, project_type: str) -> Dict[str, Any]:
        """Generate a team of agents for the project"""
        try:
            payload = {
                "prompt": prompt,
                "project_type": project_type,
                "methodology": "BMAD",
                "agent_count": 4
            }
            
            async with self.session.post(f"{self.base_url}/generate-team", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    # Fallback to default team
                    return self._create_default_team(project_type)
        except Exception as e:
            logger.warning(f"Archon service unavailable, using default team: {e}")
            return self._create_default_team(project_type)
    
    def _create_default_team(self, project_type: str) -> Dict[str, Any]:
        """Create a default agent team when Archon is unavailable"""
        return {
            "team_id": f"default_{project_type}",
            "agents": [
                {
                    "name": "Requirements Analyst",
                    "role": "analyst",
                    "capabilities": ["requirements_analysis", "user_story_creation"],
                    "model": "gpt-4"
                },
                {
                    "name": "System Architect",
                    "role": "architect", 
                    "capabilities": ["system_design", "technology_selection"],
                    "model": "gpt-4"
                },
                {
                    "name": "Full Stack Developer",
                    "role": "developer",
                    "capabilities": ["frontend_development", "backend_development"],
                    "model": "gpt-4"
                },
                {
                    "name": "Quality Assurance",
                    "role": "qa",
                    "capabilities": ["testing", "validation", "documentation"],
                    "model": "gpt-4"
                }
            ]
        }


class PheromindAdapter(ComponentAdapter):
    """Adapter for Pheromind coordination service"""
    
    def __init__(self):
        super().__init__(os.getenv("PHEROMIND_URL", "http://localhost:8502"))
    
    async def initialize_pheromone_bus(self, agent_team: Dict[str, Any], project_path: str, project_id: str) -> Dict[str, Any]:
        """Initialize pheromone bus for agent coordination"""
        try:
            payload = {
                "project_id": project_id,
                "project_path": project_path,
                "agents": agent_team.get("agents", []),
                "coordination_mode": "collaborative"
            }
            
            async with self.session.post(f"{self.base_url}/initialize-bus", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return self._create_fallback_bus(project_id)
        except Exception as e:
            logger.warning(f"Pheromind service unavailable, using fallback: {e}")
            return self._create_fallback_bus(project_id)
    
    async def drop_pheromone(self, pheromone_type: str, data: Dict[str, Any], project_id: str, 
                           agent_id: str = None, trail_id: str = None) -> bool:
        """Drop a pheromone for agent coordination"""
        try:
            payload = {
                "type": pheromone_type,
                "data": data,
                "project_id": project_id,
                "agent_id": agent_id,
                "trail_id": trail_id,
                "timestamp": datetime.now().isoformat()
            }
            
            async with self.session.post(f"{self.base_url}/drop-pheromone", json=payload) as response:
                return response.status == 200
        except Exception as e:
            logger.debug(f"Failed to drop pheromone: {e}")
            return False
    
    def _create_fallback_bus(self, project_id: str) -> Dict[str, Any]:
        """Create fallback bus configuration"""
        return {
            "bus_id": f"fallback_{project_id}",
            "status": "fallback",
            "coordination_mode": "sequential"
        }


class BMADAdapter(ComponentAdapter):
    """Adapter for BMAD-METHOD workflow service"""
    
    def __init__(self):
        super().__init__(os.getenv("BMAD_URL", "http://localhost:8503"))
    
    async def execute_workflow_phase(self, phase: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a specific BMAD workflow phase"""
        try:
            payload = {
                "phase": phase,
                "context": context,
                "methodology": "BMAD"
            }
            
            async with self.session.post(f"{self.base_url}/execute-phase", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return self._simulate_phase_execution(phase, context)
        except Exception as e:
            logger.warning(f"BMAD service unavailable, simulating phase: {e}")
            return self._simulate_phase_execution(phase, context)
    
    def _simulate_phase_execution(self, phase: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate phase execution when BMAD service is unavailable"""
        return {
            "phase": phase,
            "status": "simulated",
            "outputs": [],
            "summary": f"Simulated execution of {phase} phase",
            "success": True
        }


class MCPAdapter(ComponentAdapter):
    """Adapter for MCP-Crawl4AI-RAG research service"""
    
    def __init__(self):
        super().__init__(os.getenv("MCP_URL", "http://localhost:8051"))
    
    async def research_technologies(self, project_type: str, requirements: List[str]) -> Dict[str, Any]:
        """Research relevant technologies and best practices"""
        try:
            payload = {
                "project_type": project_type,
                "requirements": requirements,
                "research_depth": "comprehensive"
            }
            
            async with self.session.post(f"{self.base_url}/research", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return self._provide_default_research(project_type)
        except Exception as e:
            logger.warning(f"MCP service unavailable, using default research: {e}")
            return self._provide_default_research(project_type)
    
    def _provide_default_research(self, project_type: str) -> Dict[str, Any]:
        """Provide default technology recommendations"""
        tech_stacks = {
            "fullstack": {
                "frontend": ["React", "TypeScript", "Tailwind CSS"],
                "backend": ["Node.js", "Express", "PostgreSQL"],
                "deployment": ["Docker", "Nginx"]
            },
            "frontend": {
                "framework": ["React", "TypeScript"],
                "styling": ["Tailwind CSS", "CSS Modules"],
                "build": ["Vite", "ESLint", "Prettier"]
            },
            "backend": {
                "runtime": ["Node.js", "Express"],
                "database": ["PostgreSQL", "Prisma"],
                "auth": ["JWT", "bcrypt"]
            },
            "mobile": {
                "framework": ["React Native", "Expo"],
                "state": ["Redux Toolkit"],
                "navigation": ["React Navigation"]
            }
        }
        
        return {
            "project_type": project_type,
            "recommended_stack": tech_stacks.get(project_type, tech_stacks["fullstack"]),
            "best_practices": [
                "Use TypeScript for type safety",
                "Implement comprehensive testing",
                "Follow clean code principles",
                "Use version control with Git"
            ],
            "source": "default_recommendations"
        }


class ComponentManager:
    """Manager for all component adapters"""
    
    def __init__(self):
        self.archon = ArchonAdapter()
        self.pheromind = PheromindAdapter()
        self.bmad = BMADAdapter()
        self.mcp = MCPAdapter()
    
    async def __aenter__(self):
        await self.archon.__aenter__()
        await self.pheromind.__aenter__()
        await self.bmad.__aenter__()
        await self.mcp.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.archon.__aexit__(exc_type, exc_val, exc_tb)
        await self.pheromind.__aexit__(exc_type, exc_val, exc_tb)
        await self.bmad.__aexit__(exc_type, exc_val, exc_tb)
        await self.mcp.__aexit__(exc_type, exc_val, exc_tb)
    
    async def health_check_all(self) -> Dict[str, Any]:
        """Check health of all components"""
        results = {}
        
        components = [
            ("archon", self.archon),
            ("pheromind", self.pheromind),
            ("bmad", self.bmad),
            ("mcp", self.mcp)
        ]
        
        for name, component in components:
            try:
                results[name] = await component.health_check()
            except Exception as e:
                results[name] = {"status": "error", "error": str(e)}
        
        return results
