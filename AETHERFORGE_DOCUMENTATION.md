# Aetherforge - Autonomous AI Software Creation System

## 🔮 Overview

Aetherforge is a revolutionary autonomous AI software creation system that generates complete, production-ready software projects from natural language descriptions. It combines multiple AI agents, advanced coordination mechanisms, and real-time monitoring to create a seamless development experience.

## 🏗️ System Architecture

### Core Components

1. **Orchestrator** (`src/orchestrator.py`)
   - Central coordination hub
   - FastAPI-based REST API
   - Project lifecycle management
   - Agent coordination and workflow execution

2. **Pheromone System** (`src/pheromone_system.py`)
   - Real-time agent communication
   - Event-driven coordination
   - WebSocket-based messaging
   - Project progress tracking

3. **Component Adapters** (`src/component_adapters_real.py`)
   - HTTP clients for external services
   - Fallback mechanisms
   - Health monitoring
   - Service integration

4. **Project Generator** (`src/project_generator_standalone.py`)
   - Standalone project creation
   - Complete file generation
   - Technology stack setup
   - Production-ready output

5. **VS Code Extension** (`src/aetherforge.ts`)
   - User interface
   - Real-time monitoring
   - Project management
   - Component initialization

### External Components

1. **Archon** - Agent team generation and management
2. **Pheromind** - Advanced agent coordination and visualization
3. **MCP-Crawl4AI-RAG** - Web research and documentation
4. **BMAD-METHOD** - Development methodology framework

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Node.js 18+
- VS Code
- Docker (optional, for component services)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd TaoForge
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install VS Code extension:**
   - Open VS Code
   - Install the Aetherforge extension from the extensions folder
   - Or use: `code --install-extension aetherforge-0.1.0.vsix`

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

### Basic Usage

1. **Start the Orchestrator:**
   ```bash
   python src/orchestrator.py
   ```

2. **Open VS Code and activate Aetherforge:**
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Aetherforge: Start"
   - Select the command to open the Aetherforge panel

3. **Create your first project:**
   - Enter a project description in the panel
   - Select project type and options
   - Click "Create Project"
   - Watch as your project is generated automatically!

## 📋 Features

### ✅ Completed Features

- **Real-time Project Generation**: Complete software projects from natural language
- **Multi-Agent Coordination**: Specialized AI agents for different development phases
- **Pheromone-based Communication**: Advanced agent coordination system
- **Component Integration**: Seamless integration with external AI services
- **VS Code Integration**: Full-featured extension with real-time monitoring
- **Project Management**: Track and manage generated projects
- **Fallback Mechanisms**: Robust operation even when external services are unavailable
- **Production-ready Output**: Generated projects include Docker, CI/CD, and deployment configs

### 🔄 Workflow Phases

1. **Requirements Analysis** - AI analyst creates detailed requirements and user stories
2. **System Architecture** - AI architect designs system structure and technology stack
3. **Development Planning** - Create detailed development plan and project structure
4. **Core Development** - AI developer implements frontend, backend, and database
5. **Testing & Validation** - AI QA engineer creates tests and validates functionality
6. **Documentation** - Generate comprehensive documentation and deployment guides

### 🛠️ Supported Project Types

- **Full Stack Web Applications** (React + Express.js + Database)
- **Frontend Applications** (React, Vue, Angular)
- **Backend APIs** (Express.js, FastAPI, Django)
- **Mobile Applications** (React Native, Flutter)
- **Desktop Applications** (Electron, Tauri)
- **Microservices** (Docker-based service architectures)

## 🔧 Configuration

### Environment Variables

```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key_here

# Service URLs
ARCHON_URL=http://localhost:8100
PHEROMIND_URL=http://localhost:8502
MCP_URL=http://localhost:8051
BMAD_URL=http://localhost:8503

# Paths
PROJECTS_DIR=./projects
PHEROMONE_FILE=./pheromones.json

# Server Configuration
HOST=0.0.0.0
PORT=8000
```

### VS Code Settings

```json
{
  "aetherforge.orchestratorUrl": "http://localhost:8000",
  "aetherforge.projectsPath": "./projects",
  "aetherforge.autoRefresh": true,
  "aetherforge.enableNotifications": true
}
```

## 📊 API Reference

### Orchestrator API

#### Create Project
```http
POST /projects
Content-Type: application/json

{
  "prompt": "Create a todo list application with user authentication",
  "project_name": "MyTodoApp",
  "project_type": "fullstack",
  "workflow": "greenfield-fullstack"
}
```

#### Get Project Status
```http
GET /projects/{project_id}/status
```

#### List Projects
```http
GET /projects
```

#### Component Status
```http
GET /components/status
```

#### Pheromone System
```http
GET /pheromones
POST /pheromones
GET /pheromones/statistics
```

### Response Examples

#### Project Creation Response
```json
{
  "status": "success",
  "project_id": "uuid-here",
  "project_slug": "my-todo-app",
  "project_path": "/path/to/projects/my-todo-app",
  "message": "Project creation started",
  "workflow": "greenfield-fullstack",
  "agent_team": {
    "team_id": "team_123",
    "agents": [...]
  }
}
```

#### Component Status Response
```json
{
  "status": "success",
  "components": {
    "archon": {
      "status": "healthy",
      "service": "Archon",
      "response_time": "0.123s"
    },
    "pheromind": {
      "status": "healthy",
      "service": "Pheromind"
    }
  },
  "summary": {
    "healthy": 4,
    "total": 4,
    "health_percentage": 100
  }
}
```

## 🧪 Testing

### Run Unit Tests
```bash
# Test orchestrator functionality
python test_project_generation.py

# Test standalone generator
python test_standalone_generator.py

# Test pheromone system
python -m pytest src/test_pheromone_system.py
```

### Integration Tests
```bash
# Test complete workflow
python test_integration.py

# Test component adapters
python test_component_adapters.py
```

## 🐛 Troubleshooting

### Common Issues

1. **Orchestrator won't start**
   - Check Python dependencies: `pip install -r requirements.txt`
   - Verify port 8000 is available
   - Check environment variables

2. **VS Code extension not working**
   - Ensure extension is properly installed
   - Check VS Code developer console for errors
   - Verify orchestrator is running

3. **Project generation fails**
   - Check OpenAI API key is set
   - Verify internet connection
   - Check orchestrator logs for errors

4. **Components show as offline**
   - External components are optional
   - System works with fallback mechanisms
   - Check Docker if using containerized components

### Debug Mode

Enable debug logging:
```bash
export AETHERFORGE_DEBUG=true
python src/orchestrator.py
```

## 📈 Performance

### Benchmarks

- **Project Generation Time**: 2-5 minutes for full-stack applications
- **File Generation**: 15-30 files per project
- **Memory Usage**: ~200MB for orchestrator
- **API Response Time**: <100ms for status endpoints

### Optimization Tips

1. Use SSD storage for faster file operations
2. Ensure stable internet connection for AI API calls
3. Close unnecessary applications during generation
4. Use local components when possible for better performance

## 🔒 Security

### Best Practices

1. **API Keys**: Store in environment variables, never commit to version control
2. **Network**: Run on localhost by default, use HTTPS in production
3. **File Permissions**: Generated projects use secure file permissions
4. **Input Validation**: All user inputs are validated and sanitized

### Security Features

- Input sanitization for project descriptions
- Path traversal protection
- Rate limiting on API endpoints
- Secure file generation with proper permissions

## 🤝 Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style

- Python: Follow PEP 8
- TypeScript: Use ESLint configuration
- Documentation: Update relevant docs

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- VS Code team for excellent extension APIs
- FastAPI for the web framework
- All contributors and testers

---

**Generated by Aetherforge v1.0.0** - Autonomous AI Software Creation System
