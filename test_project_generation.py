#!/usr/bin/env python3
"""
Test Project Generation Engine
Tests the actual project generation functionality
"""

import os
import sys
import asyncio
import tempfile
import shutil
from pathlib import Path
import json

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_project_generation():
    """Test the complete project generation pipeline"""
    
    print("🔮 Testing Aetherforge Project Generation Engine")
    print("=" * 60)
    
    # Create temporary directory for test
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = Path(temp_dir) / "test_todo_app"
        project_path.mkdir()
        
        print(f"📁 Test project directory: {project_path}")
        
        # Test 1: Initialize project structure
        print("\n1. Testing project structure initialization...")
        try:
            from orchestrator import initialize_project_structure
            
            await initialize_project_structure(str(project_path), "test_project_123")
            
            # Check if directories were created
            expected_dirs = ["src", "docs", "tests", "config"]
            for dir_name in expected_dirs:
                dir_path = project_path / dir_name
                if dir_path.exists():
                    print(f"   ✅ {dir_name}/ directory created")
                else:
                    print(f"   ❌ {dir_name}/ directory missing")
            
            # Check if README was created
            readme_path = project_path / "README.md"
            if readme_path.exists():
                print("   ✅ README.md created")
                print(f"   📄 README content preview: {readme_path.read_text()[:100]}...")
            else:
                print("   ❌ README.md missing")
                
        except Exception as e:
            print(f"   ❌ Project structure initialization failed: {e}")
        
        # Test 2: Test agent execution
        print("\n2. Testing agent execution...")
        try:
            from orchestrator import execute_analyst_agent_real
            
            # Mock context for analyst
            context = {
                'prompt': 'Create a simple todo list application with user authentication',
                'project_path': str(project_path),
                'project_id': 'test_project_123'
            }
            
            # Only test if OpenAI API key is available
            if os.getenv("OPENAI_API_KEY"):
                print("   🔑 OpenAI API key found, testing real agent execution...")
                result = await execute_analyst_agent_real(context)
                
                if result.get("success"):
                    print(f"   ✅ Analyst agent executed successfully")
                    print(f"   📄 Files created: {result.get('outputs', [])}")
                    
                    # Check if files were actually created
                    for output_file in result.get('outputs', []):
                        file_path = project_path / output_file
                        if file_path.exists():
                            print(f"   ✅ {output_file} file exists")
                        else:
                            print(f"   ❌ {output_file} file missing")
                else:
                    print(f"   ❌ Analyst agent failed: {result.get('error', 'Unknown error')}")
            else:
                print("   ⚠️  No OpenAI API key found, skipping real agent test")
                
        except Exception as e:
            print(f"   ❌ Agent execution test failed: {e}")
        
        # Test 3: Test file generation functions
        print("\n3. Testing file generation functions...")
        try:
            from orchestrator import generate_frontend_code, generate_backend_code, generate_config_files
            
            # Test frontend generation
            if os.getenv("OPENAI_API_KEY"):
                print("   🔄 Testing frontend code generation...")
                frontend_outputs = await generate_frontend_code(
                    "Create a todo list app", 
                    project_path, 
                    "React TypeScript application"
                )
                print(f"   📄 Frontend files: {frontend_outputs}")
                
                # Check if React files were created
                for output_file in frontend_outputs:
                    file_path = project_path / output_file
                    if file_path.exists():
                        print(f"   ✅ {output_file} created")
                        # Show preview of content
                        content = file_path.read_text()[:200]
                        print(f"   📄 Preview: {content}...")
                    else:
                        print(f"   ❌ {output_file} missing")
            
            # Test backend generation
            if os.getenv("OPENAI_API_KEY"):
                print("   🔄 Testing backend code generation...")
                backend_outputs = await generate_backend_code(
                    "Create a todo list app",
                    project_path,
                    "Express.js TypeScript API"
                )
                print(f"   📄 Backend files: {backend_outputs}")
                
                for output_file in backend_outputs:
                    file_path = project_path / output_file
                    if file_path.exists():
                        print(f"   ✅ {output_file} created")
                    else:
                        print(f"   ❌ {output_file} missing")
            
            # Test config generation (doesn't need OpenAI)
            print("   🔄 Testing configuration file generation...")
            config_outputs = await generate_config_files(project_path)
            print(f"   📄 Config files: {config_outputs}")
            
            for output_file in config_outputs:
                file_path = project_path / output_file
                if file_path.exists():
                    print(f"   ✅ {output_file} created")
                else:
                    print(f"   ❌ {output_file} missing")
                    
        except Exception as e:
            print(f"   ❌ File generation test failed: {e}")
        
        # Test 4: Test complete workflow execution
        print("\n4. Testing complete workflow execution...")
        try:
            from orchestrator import get_default_agent_team
            
            # Get a default agent team
            agent_team = get_default_agent_team("greenfield-fullstack")
            print(f"   👥 Agent team: {len(agent_team.get('agents', []))} agents")
            
            # Test pheromone bus initialization
            pheromone_bus = {"bus_id": "test_bus", "status": "active"}
            print("   📡 Pheromone bus initialized")
            
            # Test project metadata creation
            metadata = {
                "project_id": "test_project_123",
                "name": "TestTodoApp",
                "type": "fullstack",
                "status": "completed",
                "created_at": "2024-01-01T00:00:00Z",
                "agents_used": [agent["name"] for agent in agent_team.get("agents", [])],
                "files_generated": []
            }
            
            metadata_file = project_path / ".aetherforge.json"
            metadata_file.write_text(json.dumps(metadata, indent=2))
            
            if metadata_file.exists():
                print("   ✅ Project metadata created")
            else:
                print("   ❌ Project metadata missing")
                
        except Exception as e:
            print(f"   ❌ Workflow test failed: {e}")
        
        # Test 5: Validate project structure
        print("\n5. Validating final project structure...")
        
        all_files = []
        for root, dirs, files in os.walk(project_path):
            for file in files:
                rel_path = os.path.relpath(os.path.join(root, file), project_path)
                all_files.append(rel_path)
        
        print(f"   📁 Total files created: {len(all_files)}")
        
        # Expected files for a complete project
        expected_files = [
            "README.md",
            ".aetherforge.json",
            ".gitignore",
            ".env.example",
            "Dockerfile",
            "docker-compose.yml"
        ]
        
        for expected_file in expected_files:
            if expected_file in all_files:
                print(f"   ✅ {expected_file}")
            else:
                print(f"   ❌ {expected_file} missing")
        
        # Show all created files
        print(f"\n📄 All files created:")
        for file_path in sorted(all_files):
            print(f"   - {file_path}")
        
        print(f"\n🎉 Project generation test completed!")
        print(f"📊 Results: {len(all_files)} files created in {project_path}")

def main():
    """Main test function"""
    
    # Check prerequisites
    print("🔍 Checking prerequisites...")
    
    # Check if OpenAI API key is set
    if os.getenv("OPENAI_API_KEY"):
        print("✅ OpenAI API key found")
    else:
        print("⚠️  OpenAI API key not found - some tests will be skipped")
        print("   Set OPENAI_API_KEY environment variable for full testing")
    
    # Check if src directory exists
    if os.path.exists("src"):
        print("✅ Source directory found")
    else:
        print("❌ Source directory not found")
        return 1
    
    # Run the test
    try:
        asyncio.run(test_project_generation())
        print("\n🎉 All tests completed successfully!")
        return 0
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
