global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Aetherforge Orchestrator
  - job_name: 'aetherforge-orchestrator'
    static_configs:
      - targets: ['orchestrator:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Archon Service
  - job_name: 'aetherforge-archon'
    static_configs:
      - targets: ['archon:8100']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # MCP-Crawl4AI Service
  - job_name: 'aetherforge-mcp'
    static_configs:
      - targets: ['mcp-crawl4ai:8051']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Pheromind Service
  - job_name: 'aetherforge-pheromind'
    static_configs:
      - targets: ['pheromind:8502']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # BMAD Service
  - job_name: 'aetherforge-bmad'
    static_configs:
      - targets: ['bmad:8503']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # Node Exporter (if added)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker containers (if docker exporter is added)
  - job_name: 'docker'
    static_configs:
      - targets: ['docker-exporter:9323']
    scrape_interval: 30s

# Custom recording rules for Aetherforge metrics
recording_rules:
  - name: aetherforge.rules
    rules:
      - record: aetherforge:project_creation_rate
        expr: rate(aetherforge_projects_created_total[5m])
      
      - record: aetherforge:project_success_rate
        expr: rate(aetherforge_projects_completed_total[5m]) / rate(aetherforge_projects_created_total[5m])
      
      - record: aetherforge:average_project_duration
        expr: avg(aetherforge_project_duration_seconds)
      
      - record: aetherforge:pheromone_activity_rate
        expr: rate(aetherforge_pheromones_dropped_total[5m])
      
      - record: aetherforge:component_availability
        expr: up{job=~"aetherforge-.*"}

# Alerting rules
alerting_rules:
  - name: aetherforge.alerts
    rules:
      - alert: AetherforgeComponentDown
        expr: up{job=~"aetherforge-.*"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Aetherforge component {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute."

      - alert: HighProjectFailureRate
        expr: aetherforge:project_success_rate < 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High project failure rate detected"
          description: "Project success rate is {{ $value | humanizePercentage }} over the last 5 minutes."

      - alert: DatabaseConnectionIssues
        expr: up{job="postgres"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been unreachable for more than 30 seconds."

      - alert: RedisConnectionIssues
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: warning
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been unreachable for more than 30 seconds."

      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Container {{ $labels.name }} is using {{ $value | humanizePercentage }} of its memory limit."

      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "Container {{ $labels.name }} is using {{ $value | humanizePercentage }} CPU over the last 5 minutes."

      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10% on {{ $labels.device }}."
