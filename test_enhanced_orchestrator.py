#!/usr/bin/env python3
"""
Test Enhanced Orchestrator
Tests the enhanced orchestrator with comprehensive error handling and configuration
"""

import os
import sys
import asyncio
import json
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_enhanced_orchestrator():
    """Test the enhanced orchestrator functionality"""
    
    print("🔮 Testing Enhanced Aetherforge Orchestrator")
    print("=" * 60)
    
    test_results = {
        "configuration": False,
        "error_handling": False,
        "project_validation": False,
        "enhanced_agents": False,
        "logging_system": False,
        "api_endpoints": False
    }
    
    # Test 1: Configuration System
    print("\n1. Testing Enhanced Configuration System...")
    try:
        from orchestrator import OrchestratorConfig, ProjectType, AgentBehavior, Priority
        
        config = OrchestratorConfig()
        print(f"   ✅ Configuration loaded successfully")
        print(f"      Projects directory: {config.projects_dir}")
        print(f"      Max concurrent projects: {config.max_concurrent_projects}")
        print(f"      Default agent behavior: {config.default_agent_behavior}")
        print(f"      Project timeout: {config.project_timeout}s")
        
        # Test enums
        print(f"   ✅ Project types: {len(ProjectType)} types available")
        print(f"   ✅ Agent behaviors: {len(AgentBehavior)} behaviors available")
        print(f"   ✅ Priority levels: {len(Priority)} levels available")
        
        test_results["configuration"] = True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
    
    # Test 2: Error Handling Classes
    print("\n2. Testing Enhanced Error Handling...")
    try:
        from orchestrator import (
            AetherforgeError, ProjectCreationError, 
            WorkflowExecutionError, AgentExecutionError
        )
        
        # Test custom error creation
        test_error = ProjectCreationError(
            "Test error message",
            error_code="TEST_ERROR",
            details={"test": "data"}
        )
        
        print(f"   ✅ Custom error classes working")
        print(f"      Error message: {test_error.message}")
        print(f"      Error code: {test_error.error_code}")
        print(f"      Error details: {test_error.details}")
        
        test_results["error_handling"] = True
        
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
    
    # Test 3: Project Validation
    print("\n3. Testing Project Validation...")
    try:
        from orchestrator import validate_project_request, ProjectRequest, ProjectType, AgentBehavior
        
        # Test valid request
        valid_request = ProjectRequest(
            prompt="Create a comprehensive web application for task management",
            project_name="TaskMaster",
            project_type=ProjectType.FULLSTACK,
            agent_behavior=AgentBehavior.BALANCED,
            test_coverage_target=0.85
        )
        
        await validate_project_request(valid_request)
        print(f"   ✅ Valid request passed validation")
        
        # Test invalid request (short prompt)
        try:
            invalid_request = ProjectRequest(
                prompt="Short",
                project_type=ProjectType.FRONTEND
            )
            await validate_project_request(invalid_request)
            print(f"   ❌ Invalid request should have failed")
        except Exception as e:
            print(f"   ✅ Invalid request properly rejected: {type(e).__name__}")
        
        test_results["project_validation"] = True
        
    except Exception as e:
        print(f"   ❌ Project validation test failed: {e}")
    
    # Test 4: Enhanced Agent Generation
    print("\n4. Testing Enhanced Agent Generation...")
    try:
        from orchestrator import generate_enhanced_agent_team, setup_project_logging
        
        # Create temporary project for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / "test_project"
            project_path.mkdir()
            
            project_logger = setup_project_logging("test_123", project_path)
            
            test_request = ProjectRequest(
                prompt="Create a mobile application for fitness tracking",
                project_type=ProjectType.MOBILE,
                agent_behavior=AgentBehavior.CREATIVE,
                enable_parallel_execution=True
            )
            
            agent_team = await generate_enhanced_agent_team(
                test_request, "mobile_app_workflow", "test_123", project_logger
            )
            
            print(f"   ✅ Enhanced agent team generated")
            print(f"      Team ID: {agent_team['team_id']}")
            print(f"      Number of agents: {len(agent_team['agents'])}")
            print(f"      Behavior: {agent_team['behavior']}")
            print(f"      Parallel execution: {agent_team['parallel_execution']}")
            
            # Check agent roles
            roles = [agent['role'] for agent in agent_team['agents']]
            print(f"      Agent roles: {', '.join(roles)}")
            
            # Check for UI designer (should be present for mobile projects)
            has_ui_designer = any(agent['role'] == 'ui_designer' for agent in agent_team['agents'])
            if has_ui_designer:
                print(f"   ✅ UI Designer agent added for mobile project")
            
        test_results["enhanced_agents"] = True
        
    except Exception as e:
        print(f"   ❌ Enhanced agent generation test failed: {e}")
    
    # Test 5: Logging System
    print("\n5. Testing Enhanced Logging System...")
    try:
        from orchestrator import setup_enhanced_logging, setup_project_logging
        
        # Test main logging setup
        main_logger = setup_enhanced_logging()
        print(f"   ✅ Enhanced logging system initialized")
        print(f"      Logger name: {main_logger.name}")
        print(f"      Handler count: {len(main_logger.handlers)}")
        
        # Test project-specific logging
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / "test_project"
            project_path.mkdir()
            
            project_logger = setup_project_logging("test_456", project_path)
            project_logger.info("Test log message")
            
            # Check if log file was created
            log_file = project_path / "logs" / "project.log"
            if log_file.exists():
                print(f"   ✅ Project log file created: {log_file}")
            
        test_results["logging_system"] = True
        
    except Exception as e:
        print(f"   ❌ Logging system test failed: {e}")
    
    # Test 6: API Endpoints (basic import test)
    print("\n6. Testing API Endpoints...")
    try:
        from orchestrator import app
        
        # Check if FastAPI app is created
        print(f"   ✅ FastAPI app created: {app.title}")
        print(f"      Version: {app.version}")
        print(f"      Description: {app.description}")
        
        # Check routes (basic test)
        routes = [route.path for route in app.routes if hasattr(route, 'path')]
        print(f"   ✅ API routes available: {len(routes)} routes")
        
        # Check for key endpoints
        key_endpoints = ["/projects", "/health", "/components/status", "/workflows"]
        available_endpoints = [ep for ep in key_endpoints if ep in routes]
        print(f"      Key endpoints: {', '.join(available_endpoints)}")
        
        test_results["api_endpoints"] = True
        
    except Exception as e:
        print(f"   ❌ API endpoints test failed: {e}")
    
    # Test Results Summary
    print(f"\n🎉 Enhanced Orchestrator Test Results")
    print("=" * 50)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    for component, passed in test_results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {component.replace('_', ' ').title()}")
    
    print(f"\n📊 Overall Results:")
    print(f"   Tests Passed: {passed_tests}/{total_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL ENHANCED FEATURES WORKING! Orchestrator is fully enhanced!")
        return True
    elif passed_tests >= total_tests * 0.8:  # 80% pass rate
        print(f"\n✅ MOSTLY ENHANCED! Orchestrator has advanced capabilities.")
        return True
    else:
        print(f"\n⚠️  PARTIAL ENHANCEMENT. Some features need attention.")
        return False

def main():
    """Main test function"""
    
    print("🔍 Enhanced Aetherforge Orchestrator Test")
    print("This test verifies all enhanced features are working")
    print()
    
    # Check prerequisites
    required_files = [
        "src/orchestrator.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return 1
    
    print("✅ All required files found")
    
    # Run the enhanced orchestrator test
    try:
        success = asyncio.run(test_enhanced_orchestrator())
        if success:
            print("\n🚀 Enhanced Orchestrator is ready for advanced autonomous software creation!")
            return 0
        else:
            print("\n🔧 Some enhanced features need attention.")
            return 1
    except Exception as e:
        print(f"\n❌ Enhanced orchestrator test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
