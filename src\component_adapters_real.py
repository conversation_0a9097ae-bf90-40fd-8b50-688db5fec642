"""
Real Component Adapters for Aetherforge
Provides HTTP clients and integration with external component services
"""

import os
import asyncio
import aiohttp
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ComponentAdapter:
    """Base class for component adapters"""
    
    def __init__(self, service_url: str, service_name: str, timeout: int = 30):
        self.service_url = service_url
        self.service_name = service_name
        self.timeout = timeout
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check if the service is healthy"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5))
            
            async with self.session.get(f"{self.service_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "status": "healthy",
                        "service": self.service_name,
                        "response_time": response.headers.get("X-Response-Time", "unknown"),
                        "data": data
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "service": self.service_name,
                        "status_code": response.status,
                        "error": f"HTTP {response.status}"
                    }
        except asyncio.TimeoutError:
            return {
                "status": "timeout",
                "service": self.service_name,
                "error": "Service timeout"
            }
        except Exception as e:
            return {
                "status": "unreachable",
                "service": self.service_name,
                "error": str(e)
            }

class ArchonAdapter(ComponentAdapter):
    """Adapter for Archon agent team generation service"""
    
    def __init__(self, service_url: str = None):
        super().__init__(
            service_url or os.getenv("ARCHON_URL", "http://localhost:8100"),
            "Archon",
            timeout=60
        )
    
    async def generate_agent_team(self, prompt: str, workflow: str, project_id: str) -> Dict[str, Any]:
        """Generate an agent team for the project"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
            
            payload = {
                "prompt": prompt,
                "workflow": workflow,
                "project_id": project_id,
                "timestamp": datetime.now().isoformat()
            }
            
            async with self.session.post(f"{self.service_url}/generate-team", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Archon API error {response.status}: {error_text}")
                    
        except Exception as e:
            logger.warning(f"Archon service unavailable: {e}")
            # Return fallback team
            return self._get_fallback_team(workflow, project_id)
    
    def _get_fallback_team(self, workflow: str, project_id: str) -> Dict[str, Any]:
        """Generate a fallback team when Archon is unavailable"""
        return {
            "team_id": f"fallback_{project_id}",
            "workflow": workflow,
            "agents": [
                {
                    "id": "analyst_fallback",
                    "name": "Requirements Analyst",
                    "role": "analyst",
                    "capabilities": ["requirements_analysis", "user_stories"],
                    "model": "gpt-4"
                },
                {
                    "id": "architect_fallback",
                    "name": "System Architect", 
                    "role": "architect",
                    "capabilities": ["system_design", "technology_selection"],
                    "model": "gpt-4"
                },
                {
                    "id": "developer_fallback",
                    "name": "Full Stack Developer",
                    "role": "developer", 
                    "capabilities": ["frontend_development", "backend_development"],
                    "model": "gpt-4"
                },
                {
                    "id": "qa_fallback",
                    "name": "QA Engineer",
                    "role": "qa",
                    "capabilities": ["testing", "validation"],
                    "model": "gpt-4"
                }
            ],
            "fallback": True,
            "generated_at": datetime.now().isoformat()
        }

class PheromindAdapter(ComponentAdapter):
    """Adapter for Pheromind coordination service"""
    
    def __init__(self, service_url: str = None):
        super().__init__(
            service_url or os.getenv("PHEROMIND_URL", "http://localhost:8502"),
            "Pheromind",
            timeout=30
        )
    
    async def initialize_pheromone_bus(self, agent_team: Dict[str, Any], project_path: str, project_id: str) -> Dict[str, Any]:
        """Initialize pheromone bus for agent coordination"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
            
            payload = {
                "agent_team": agent_team,
                "project_path": project_path,
                "project_id": project_id,
                "timestamp": datetime.now().isoformat()
            }
            
            async with self.session.post(f"{self.service_url}/initialize-bus", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"Pheromind API error {response.status}: {error_text}")
                    
        except Exception as e:
            logger.warning(f"Pheromind service unavailable: {e}")
            # Return fallback bus configuration
            return {
                "bus_id": f"local_bus_{project_id}",
                "status": "fallback",
                "project_id": project_id,
                "agents_connected": len(agent_team.get("agents", [])),
                "fallback": True,
                "initialized_at": datetime.now().isoformat()
            }

class MCPAdapter(ComponentAdapter):
    """Adapter for MCP-Crawl4AI-RAG research service"""
    
    def __init__(self, service_url: str = None):
        super().__init__(
            service_url or os.getenv("MCP_URL", "http://localhost:8051"),
            "MCP-Crawl4AI-RAG",
            timeout=120
        )
    
    async def research_technologies(self, project_type: str, keywords: List[str]) -> Dict[str, Any]:
        """Research technologies and best practices for the project"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
            
            payload = {
                "project_type": project_type,
                "keywords": keywords,
                "research_depth": "comprehensive",
                "timestamp": datetime.now().isoformat()
            }
            
            async with self.session.post(f"{self.service_url}/research", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"MCP API error {response.status}: {error_text}")
                    
        except Exception as e:
            logger.warning(f"MCP service unavailable: {e}")
            # Return fallback research data
            return self._get_fallback_research(project_type, keywords)
    
    def _get_fallback_research(self, project_type: str, keywords: List[str]) -> Dict[str, Any]:
        """Generate fallback research data when MCP is unavailable"""
        
        fallback_stacks = {
            "fullstack": {
                "frontend": ["React", "TypeScript", "Tailwind CSS"],
                "backend": ["Node.js", "Express", "TypeScript"],
                "database": ["PostgreSQL", "Prisma"],
                "deployment": ["Docker", "Nginx"]
            },
            "frontend": {
                "frontend": ["React", "TypeScript", "Vite"],
                "styling": ["Tailwind CSS", "CSS Modules"],
                "testing": ["Jest", "React Testing Library"],
                "deployment": ["Netlify", "Vercel"]
            },
            "backend": {
                "backend": ["Node.js", "Express", "TypeScript"],
                "database": ["PostgreSQL", "MongoDB"],
                "testing": ["Jest", "Supertest"],
                "deployment": ["Docker", "PM2"]
            }
        }
        
        return {
            "project_type": project_type,
            "recommended_stack": fallback_stacks.get(project_type, fallback_stacks["fullstack"]),
            "research_summary": f"Fallback technology recommendations for {project_type} project",
            "keywords_analyzed": keywords,
            "fallback": True,
            "generated_at": datetime.now().isoformat()
        }

class BMADAdapter(ComponentAdapter):
    """Adapter for BMAD methodology service"""
    
    def __init__(self, service_url: str = None):
        super().__init__(
            service_url or os.getenv("BMAD_URL", "http://localhost:8503"),
            "BMAD-METHOD",
            timeout=60
        )
    
    async def get_workflow_definition(self, workflow_name: str, project_type: str) -> Dict[str, Any]:
        """Get BMAD workflow definition"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout))
            
            payload = {
                "workflow_name": workflow_name,
                "project_type": project_type,
                "timestamp": datetime.now().isoformat()
            }
            
            async with self.session.post(f"{self.service_url}/workflow", json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"BMAD API error {response.status}: {error_text}")
                    
        except Exception as e:
            logger.warning(f"BMAD service unavailable: {e}")
            # Return fallback workflow
            return self._get_fallback_workflow(workflow_name, project_type)
    
    def _get_fallback_workflow(self, workflow_name: str, project_type: str) -> Dict[str, Any]:
        """Generate fallback workflow when BMAD is unavailable"""
        
        fallback_workflows = {
            "greenfield-fullstack": {
                "name": "Greenfield Full Stack Development",
                "phases": [
                    {"name": "requirements_analysis", "duration": "15min", "agents": ["analyst"]},
                    {"name": "system_architecture", "duration": "20min", "agents": ["architect"]},
                    {"name": "development_planning", "duration": "10min", "agents": ["architect", "developer"]},
                    {"name": "core_development", "duration": "30min", "agents": ["developer"]},
                    {"name": "testing_validation", "duration": "15min", "agents": ["qa", "developer"]},
                    {"name": "documentation", "duration": "10min", "agents": ["analyst", "developer"]}
                ],
                "methodology": "BMAD",
                "fallback": True
            }
        }
        
        return fallback_workflows.get(workflow_name, fallback_workflows["greenfield-fullstack"])

class ComponentManager:
    """Manages all component adapters"""
    
    def __init__(self):
        self.archon = ArchonAdapter()
        self.pheromind = PheromindAdapter()
        self.mcp = MCPAdapter()
        self.bmad = BMADAdapter()
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.archon.__aenter__()
        await self.pheromind.__aenter__()
        await self.mcp.__aenter__()
        await self.bmad.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.archon.__aexit__(exc_type, exc_val, exc_tb)
        await self.pheromind.__aexit__(exc_type, exc_val, exc_tb)
        await self.mcp.__aexit__(exc_type, exc_val, exc_tb)
        await self.bmad.__aexit__(exc_type, exc_val, exc_tb)
    
    async def health_check_all(self) -> Dict[str, Any]:
        """Check health of all components"""
        results = {}
        
        # Run health checks concurrently
        health_checks = await asyncio.gather(
            self.archon.health_check(),
            self.pheromind.health_check(),
            self.mcp.health_check(),
            self.bmad.health_check(),
            return_exceptions=True
        )
        
        components = ["archon", "pheromind", "mcp", "bmad"]
        for i, result in enumerate(health_checks):
            if isinstance(result, Exception):
                results[components[i]] = {
                    "status": "error",
                    "service": components[i],
                    "error": str(result)
                }
            else:
                results[components[i]] = result
        
        return results

# Global component manager instance
_component_manager = None

async def get_component_manager() -> ComponentManager:
    """Get the global component manager instance"""
    global _component_manager
    if _component_manager is None:
        _component_manager = ComponentManager()
    return _component_manager
