# 🔮 Aetherforge User Manual

**Version 1.0.0**  
**Date: June 19, 2025**

---

## Table of Contents

1. [Introduction](#introduction)
2. [System Requirements](#system-requirements)
3. [Installation Guide](#installation-guide)
4. [Configuration](#configuration)
5. [Getting Started](#getting-started)
6. [Using the VS Code Extension](#using-the-vs-code-extension)
7. [API Reference](#api-reference)
8. [Project Creation Examples](#project-creation-examples)
9. [Docker Deployment](#docker-deployment)
10. [Monitoring and Troubleshooting](#monitoring-and-troubleshooting)
11. [Advanced Configuration](#advanced-configuration)
12. [FAQ](#faq)
13. [Support](#support)

---

## Introduction

Aetherforge is a revolutionary autonomous AI software creation system that generates complete software projects from natural language descriptions. It orchestrates multiple specialized AI agents to handle every aspect of software development - from requirements analysis to deployment.

### Key Features
- **Autonomous Project Creation** - Describe your project and watch it come to life
- **Multi-Agent Architecture** - Specialized AI agents for different development phases
- **Full-Stack Support** - Complete applications with frontend, backend, and database
- **VS Code Integration** - Seamless development environment integration
- **Docker Containerization** - Production-ready deployment
- **Real-time Monitoring** - Track progress with pheromone-based coordination

### How It Works
1. **Input**: You provide a natural language description of your desired software
2. **Analysis**: Requirements Analyst agent breaks down your needs
3. **Architecture**: System Architect designs the technical solution
4. **Development**: Full Stack Developer implements the code
5. **Quality**: QA agent tests and validates the implementation
6. **Output**: Complete, working software project ready for deployment

---

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Memory**: 8 GB RAM
- **Storage**: 10 GB free space
- **Network**: Internet connection for AI services

### Recommended Requirements
- **Operating System**: Windows 11, macOS 12+, or Linux (Ubuntu 20.04+)
- **Memory**: 16 GB RAM or more
- **Storage**: 50 GB free space (for multiple projects)
- **CPU**: Multi-core processor (4+ cores recommended)
- **Network**: High-speed internet connection

### Software Dependencies
- **Python 3.8+** (Python 3.12 recommended)
- **Node.js 16+** (Node.js 18+ recommended)
- **Docker & Docker Compose** (optional but recommended)
- **VS Code** (for extension features)
- **Git** (for version control)

---

## Installation Guide

### Option 1: Quick Start (Recommended)

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-org/aetherforge.git
   cd aetherforge
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys (see Configuration section)
   ```

3. **Deploy with Docker**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

4. **Access the System**
   - Orchestrator API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

### Option 2: Local Development Setup

1. **Clone and Setup Python Environment**
   ```bash
   git clone https://github.com/your-org/aetherforge.git
   cd aetherforge
   
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the Orchestrator**
   ```bash
   uvicorn src.orchestrator:app --host 0.0.0.0 --port 8000
   ```

### Option 3: VS Code Extension Only

1. **Install VS Code Extension**
   ```bash
   cd vscode-extension
   npm install
   npm run compile
   ```

2. **Install in VS Code**
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Install from VSIX or load unpacked extension

---

## Configuration

### Environment Variables (.env file)

#### Required Configuration
```bash
# API Keys (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here  # Optional

# Basic Settings
PROJECTS_DIR=./projects
PHEROMONE_FILE=./pheromones.json
```

#### Service URLs
```bash
ORCHESTRATOR_URL=http://localhost:8000
ARCHON_URL=http://localhost:8100
MCP_URL=http://localhost:8051
PHEROMIND_URL=http://localhost:8502
BMAD_URL=http://localhost:8503
```

#### Database Configuration
```bash
POSTGRES_PASSWORD=aetherforge123
DATABASE_URL=postgresql://aetherforge:${POSTGRES_PASSWORD}@localhost:5432/aetherforge
REDIS_URL=redis://localhost:6379
```

#### Performance Settings
```bash
MAX_CONCURRENT_PROJECTS=5
PROJECT_TIMEOUT_MINUTES=30
LOG_LEVEL=info
```

#### Monitoring (Production)
```bash
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=30d
ENABLE_DOCS=true
```

### Getting API Keys

#### OpenAI API Key (Required)
1. Visit https://platform.openai.com/
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your .env file

#### Anthropic API Key (Optional)
1. Visit https://console.anthropic.com/
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your .env file

---

## Getting Started

### Your First Project

1. **Start Aetherforge**
   ```bash
   # Using Docker (recommended)
   ./deploy.sh
   
   # Or locally
   uvicorn src.orchestrator:app --host 0.0.0.0 --port 8000
   ```

2. **Verify System Health**
   ```bash
   curl http://localhost:8000/health
   ```

3. **Create Your First Project**
   ```bash
   curl -X POST http://localhost:8000/projects \
     -H "Content-Type: application/json" \
     -d '{
       "prompt": "Create a simple todo list app with user authentication",
       "project_name": "MyTodoApp",
       "project_type": "fullstack"
     }'
   ```

4. **Monitor Progress**
   - Check API response for project ID
   - Visit http://localhost:8000/docs for interactive API
   - Monitor pheromones at http://localhost:8502 (if available)

5. **Access Your Project**
   - Generated projects appear in the `projects/` directory
   - Each project includes complete documentation and setup instructions

### Project Types

| Type | Description | Generated Components |
|------|-------------|---------------------|
| `fullstack` | Complete web application | Frontend + Backend + Database |
| `frontend` | Frontend-only application | React/Vue/Angular app |
| `backend` | Backend API service | Express/FastAPI/Django API |
| `mobile` | Mobile application | React Native/Flutter app |
| `desktop` | Desktop application | Electron/Tauri app |
| `api` | REST API service | Dedicated API service |
| `game` | Game development | Basic game framework |

---

## Using the VS Code Extension

### Installation

1. **Build the Extension**
   ```bash
   cd vscode-extension
   npm install
   npm run compile
   ```

2. **Install in VS Code**
   - Press F5 to launch Extension Development Host
   - Or package as VSIX: `npm run package`

### Features

#### Create Project Tab
- **Project Description**: Detailed natural language input
- **Project Name**: Optional custom name
- **Project Type**: Dropdown selection
- **Advanced Options**: Workflow selection and feature toggles

#### System Status Tab
- **Component Health**: Real-time status of all services
- **Refresh Status**: Manual status updates
- **Initialize Components**: Start missing services

#### Projects Tab
- **Project List**: View all generated projects
- **Open Project**: Launch project in new VS Code window
- **View Details**: See project metadata and status

#### Settings Tab
- **Orchestrator URL**: Configure API endpoint
- **Projects Path**: Set local projects directory
- **Connection Test**: Verify orchestrator connectivity

### Commands

- **Aetherforge: Create Project** - Open project creation interface
- **Aetherforge: Open Dashboard** - Launch main extension UI
- **Aetherforge: Refresh Status** - Update component status

### Usage Workflow

1. **Open VS Code Extension**
   - Press Ctrl+Shift+P
   - Type "Aetherforge: Create Project"

2. **Configure Project**
   - Switch to "Create Project" tab
   - Enter detailed project description
   - Select project type and options

3. **Monitor Creation**
   - Watch real-time progress updates
   - Switch to "System Status" tab to monitor components

4. **Access Generated Project**
   - Project appears in "Projects" tab when complete
   - Click "Open" to launch in new VS Code window

---

## API Reference

### Base URL
```
http://localhost:8000
```

### Authentication
Currently, no authentication is required for local development. For production deployments, implement appropriate security measures.

### Endpoints

#### Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-19T12:00:00Z",
  "version": "1.0.0"
}
```

#### Create Project
```http
POST /projects
Content-Type: application/json

{
  "prompt": "Create a blog platform with user authentication",
  "project_name": "BlogPlatform",
  "project_type": "fullstack",
  "workflow": "greenfield-fullstack"
}
```

**Response:**
```json
{
  "status": "success",
  "project_id": "uuid-here",
  "project_slug": "BlogPlatform",
  "project_path": "projects/BlogPlatform",
  "message": "Project creation started",
  "estimated_completion": "2025-06-19T12:15:00Z"
}
```

#### List Projects
```http
GET /projects
```

#### Get Project Details
```http
GET /projects/{project_id}
```

#### Component Status
```http
GET /components/status
```

#### Pheromone Operations
```http
GET /pheromones
POST /pheromones
GET /pheromones/statistics
```

### Error Handling

All API endpoints return appropriate HTTP status codes:
- `200` - Success
- `400` - Bad Request (invalid input)
- `422` - Validation Error
- `500` - Internal Server Error

Error responses include detailed messages:
```json
{
  "error": "Invalid project type",
  "details": "Supported types: fullstack, frontend, backend, mobile, desktop, api, game"
}
```

---

## Project Creation Examples

### Example 1: Simple Todo App
```bash
curl -X POST http://localhost:8000/projects \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a todo list application where users can add, edit, delete, and mark tasks as complete. Include user authentication and data persistence.",
    "project_name": "TodoMaster",
    "project_type": "fullstack"
  }'
```

**Generated Features:**
- User registration and login
- Task CRUD operations
- Task status management
- Responsive web interface
- Database persistence
- API documentation

### Example 2: E-commerce Platform
```bash
curl -X POST http://localhost:8000/projects \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create an e-commerce platform with product catalog, shopping cart, user accounts, order management, and payment integration. Include admin dashboard for inventory management.",
    "project_name": "ShopifyClone",
    "project_type": "fullstack"
  }'
```

**Generated Features:**
- Product catalog with categories
- Shopping cart functionality
- User account management
- Order processing system
- Payment gateway integration
- Admin dashboard
- Inventory management

### Example 3: Mobile App
```bash
curl -X POST http://localhost:8000/projects \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a fitness tracking mobile app that allows users to log workouts, track progress, set goals, and view statistics. Include social features for sharing achievements.",
    "project_name": "FitTracker",
    "project_type": "mobile"
  }'
```

**Generated Features:**
- Workout logging interface
- Progress tracking charts
- Goal setting and monitoring
- Social sharing features
- Cross-platform compatibility
- Offline data storage

### Example 4: API Service
```bash
curl -X POST http://localhost:8000/projects \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a RESTful API for a library management system with endpoints for books, authors, borrowers, and transactions. Include authentication, rate limiting, and comprehensive documentation.",
    "project_name": "LibraryAPI",
    "project_type": "api"
  }'
```

**Generated Features:**
- RESTful API endpoints
- Database schema design
- Authentication middleware
- Rate limiting
- API documentation
- Error handling
- Logging and monitoring

---

## Docker Deployment

### Quick Deployment

```bash
# Clone and configure
git clone https://github.com/your-org/aetherforge.git
cd aetherforge
cp .env.example .env
# Edit .env with your API keys

# Deploy all services
./deploy.sh
```

### Manual Docker Commands

#### Development Environment
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Production Environment
```bash
# Start production services
docker-compose -f docker-compose.prod.yml up -d

# Scale orchestrator
docker-compose -f docker-compose.prod.yml up -d --scale orchestrator=3

# Monitor services
docker-compose -f docker-compose.prod.yml ps
```

### Service Management

#### Using Management Script
```bash
# Start services
./manage.sh start

# Check status
./manage.sh status

# View logs
./manage.sh logs orchestrator

# Health check
./manage.sh health

# Create test project
./manage.sh create-project

# Backup data
./manage.sh backup

# Stop services
./manage.sh stop
```

#### Individual Service Management
```bash
# Restart specific service
docker-compose restart orchestrator

# View service logs
docker-compose logs -f pheromind

# Execute commands in container
docker-compose exec orchestrator bash
```

### Service Ports

| Service | Port | Description |
|---------|------|-------------|
| Orchestrator | 8000 | Main API service |
| Archon | 8100, 8501 | Agent generation + UI |
| MCP-Crawl4AI | 8051 | Web research |
| Pheromind | 8502, 3000 | Coordination + UI |
| BMAD-METHOD | 8503 | Development methodology |
| PostgreSQL | 5432 | Database |
| Redis | 6379 | Cache |
| Prometheus | 9090 | Metrics (production) |
| Grafana | 3001 | Monitoring (production) |
| Nginx | 80, 443 | Reverse proxy (production) |

---

## Monitoring and Troubleshooting

### Health Monitoring

#### System Health Check
```bash
# Quick health check
curl http://localhost:8000/health

# Detailed component status
curl http://localhost:8000/components/status

# Pheromone statistics
curl http://localhost:8000/pheromones/statistics
```

#### Using Validation Script
```bash
# Complete system validation
python validate_system.py

# Generate validation report
python validate_system.py --report validation_report.json
```

### Common Issues and Solutions

#### Issue: Orchestrator Won't Start
**Symptoms:** Connection refused errors, health check fails

**Solutions:**
1. Check if port 8000 is available
2. Verify Python dependencies: `pip install -r requirements.txt`
3. Check environment variables in `.env`
4. Review logs: `docker-compose logs orchestrator`

#### Issue: Project Creation Fails
**Symptoms:** API returns 500 error, project not generated

**Solutions:**
1. Verify OpenAI API key is valid and has credits
2. Check network connectivity
3. Review orchestrator logs for detailed errors
4. Ensure projects directory is writable

#### Issue: VS Code Extension Not Working
**Symptoms:** Extension commands not available, UI not loading

**Solutions:**
1. Rebuild extension: `npm run compile`
2. Check VS Code developer console for errors
3. Verify orchestrator is running and accessible
4. Test connection in extension settings

#### Issue: Docker Services Not Starting
**Symptoms:** Docker compose fails, services exit immediately

**Solutions:**
1. Check Docker daemon is running
2. Verify docker-compose.yml syntax
3. Check for port conflicts
4. Review service logs: `docker-compose logs [service]`

### Logging

#### Log Levels
- **DEBUG** - Detailed debugging information
- **INFO** - General information messages
- **WARN** - Warning messages
- **ERROR** - Error messages

#### Log Locations
- **Docker**: `docker-compose logs [service]`
- **Local**: Console output or log files
- **VS Code Extension**: Developer Console (F12)

### Performance Monitoring

#### Metrics Available
- Project creation success rate
- Average project generation time
- API response times
- Component availability
- Pheromone activity rates

#### Monitoring Tools
- **Prometheus**: Metrics collection (production)
- **Grafana**: Visualization dashboards (production)
- **API Endpoints**: Real-time status via `/health` and `/components/status`

---

## Advanced Configuration

### Custom Workflows

You can define custom development workflows by modifying the workflow configuration:

```python
# In agent_executors.py
CUSTOM_WORKFLOWS = {
    "microservices-architecture": {
        "phases": [
            "requirements_analysis",
            "microservices_design", 
            "service_implementation",
            "integration_testing",
            "deployment_orchestration"
        ],
        "agents": ["analyst", "architect", "developer", "devops", "qa"]
    }
}
```

### Agent Customization

Customize agent behavior by modifying their prompts and capabilities:

```python
# Custom agent configuration
AGENT_CONFIGS = {
    "analyst": {
        "model": "gpt-4",
        "temperature": 0.3,
        "max_tokens": 2000,
        "custom_instructions": "Focus on user experience and accessibility"
    }
}
```

### Technology Preferences

Configure preferred technologies for different project types:

```python
TECH_PREFERENCES = {
    "fullstack": {
        "frontend": ["React", "TypeScript", "Tailwind CSS"],
        "backend": ["Node.js", "Express", "PostgreSQL"],
        "deployment": ["Docker", "Nginx"]
    },
    "mobile": {
        "framework": ["React Native", "Expo"],
        "state_management": ["Redux Toolkit"],
        "navigation": ["React Navigation"]
    }
}
```

### Environment-Specific Settings

#### Development
```bash
AETHERFORGE_ENV=development
DEBUG=true
LOG_LEVEL=debug
ENABLE_DOCS=true
HOT_RELOAD=true
```

#### Production
```bash
AETHERFORGE_ENV=production
DEBUG=false
LOG_LEVEL=info
ENABLE_DOCS=false
SSL_ENABLED=true
RATE_LIMITING=true
```

### Scaling Configuration

#### Horizontal Scaling
```yaml
# docker-compose.prod.yml
services:
  orchestrator:
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

#### Load Balancing
```nginx
# nginx.conf
upstream orchestrator {
    server orchestrator_1:8000;
    server orchestrator_2:8000;
    server orchestrator_3:8000;
}
```

---

## FAQ

### General Questions

**Q: What types of projects can Aetherforge create?**
A: Aetherforge can create web applications, mobile apps, desktop applications, APIs, games, and more. It supports modern frameworks like React, Vue, Angular, React Native, Flutter, Express.js, FastAPI, and Django.

**Q: How long does it take to generate a project?**
A: Simple projects take 2-5 minutes, complex projects take 5-15 minutes, and enterprise-level projects can take 15-30 minutes depending on complexity and requirements.

**Q: Do I need programming knowledge to use Aetherforge?**
A: No programming knowledge is required to generate projects. However, basic understanding of software development concepts helps in writing better project descriptions and customizing the generated code.

**Q: Can I modify the generated projects?**
A: Yes! Generated projects are fully editable and include complete documentation, making it easy to understand and modify the code according to your needs.

### Technical Questions

**Q: Which AI models does Aetherforge use?**
A: Aetherforge primarily uses OpenAI's GPT models (GPT-4 recommended) and optionally supports Anthropic's Claude models for enhanced capabilities.

**Q: Can I run Aetherforge offline?**
A: No, Aetherforge requires internet connectivity to access AI services. However, once projects are generated, they can be developed offline.

**Q: Is my project data secure?**
A: Project descriptions are sent to AI services for processing. Ensure you comply with your organization's data policies. Generated code remains on your local system.

**Q: Can I integrate Aetherforge with my existing development workflow?**
A: Yes! Generated projects include standard configuration files (package.json, requirements.txt, etc.) and can be integrated into existing CI/CD pipelines.

### Troubleshooting

**Q: Why is project creation failing?**
A: Common causes include invalid API keys, network connectivity issues, insufficient API credits, or malformed project descriptions. Check the logs for specific error messages.

**Q: The VS Code extension isn't working. What should I do?**
A: Ensure the orchestrator is running, rebuild the extension with `npm run compile`, and check the VS Code developer console for error messages.

**Q: How do I update Aetherforge?**
A: Pull the latest changes from the repository, rebuild Docker images, and restart services:
```bash
git pull origin main
docker-compose build --no-cache
docker-compose up -d
```

---

## Support

### Documentation
- **User Manual**: This document
- **API Documentation**: http://localhost:8000/docs (when running)
- **Feature Reference**: AETHERFORGE_FEATURES_COMPLETE.md

### Community
- **GitHub Repository**: https://github.com/your-org/aetherforge
- **Issues**: https://github.com/your-org/aetherforge/issues
- **Discussions**: https://github.com/your-org/aetherforge/discussions

### Professional Support
- **Email**: <EMAIL>
- **Discord**: [Join our community](https://discord.gg/aetherforge)
- **Documentation**: [docs.aetherforge.dev](https://docs.aetherforge.dev)

### Contributing
We welcome contributions! Please see our Contributing Guide for details on:
- Code contributions
- Bug reports
- Feature requests
- Documentation improvements

### License
Aetherforge is licensed under the MIT License. See the LICENSE file for details.

---

**Aetherforge v1.0.0** - Where ideas become software, autonomously. ✨

*Last updated: June 19, 2025*
