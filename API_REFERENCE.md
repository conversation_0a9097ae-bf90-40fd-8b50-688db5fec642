# Aetherforge API Reference

## 🌐 Overview

The Aetherforge Orchestrator provides a comprehensive REST API for creating and managing autonomous software projects. This reference covers all available endpoints, request/response formats, and usage examples.

**Base URL**: `http://localhost:8000`

## 🔐 Authentication

Currently, the API does not require authentication for local development. In production deployments, consider implementing API key authentication.

## 📋 Core Endpoints

### Health Check

Check if the orchestrator service is running and healthy.

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "components": {
    "orchestrator": "running",
    "pheromone_bus": "available"
  }
}
```

## 🚀 Project Management

### Create Project

Generate a new software project from a natural language description.

```http
POST /projects
Content-Type: application/json
```

**Request Body:**
```json
{
  "prompt": "Create a todo list application with user authentication and real-time updates",
  "project_name": "MyTodoApp",
  "project_type": "fullstack",
  "workflow": "greenfield-fullstack",
  "requirements": {
    "database": "postgresql",
    "frontend": "react",
    "backend": "express"
  },
  "priority": "normal",
  "deadline": "2024-02-01T00:00:00Z"
}
```

**Parameters:**
- `prompt` (required): Detailed description of the project to create
- `project_name` (optional): Custom name for the project
- `project_type` (optional): Type of project - `fullstack`, `frontend`, `backend`, `mobile`, `desktop`, `api`
- `workflow` (optional): Specific workflow to use
- `requirements` (optional): Additional technical requirements
- `priority` (optional): Priority level - `low`, `normal`, `high`
- `deadline` (optional): Project deadline in ISO format

**Response:**
```json
{
  "status": "success",
  "project_id": "550e8400-e29b-41d4-a716-446655440000",
  "project_slug": "my-todo-app",
  "project_path": "/path/to/projects/my-todo-app",
  "message": "Project creation started",
  "workflow": "greenfield-fullstack",
  "agent_team": {
    "team_id": "team_123",
    "workflow": "greenfield-fullstack",
    "agents": [
      {
        "id": "analyst_001",
        "name": "Requirements Analyst",
        "role": "analyst",
        "capabilities": ["requirements_analysis", "user_story_creation"]
      }
    ]
  }
}
```

### Get Project Status

Retrieve the current status of a specific project.

```http
GET /projects/{project_id}/status
```

**Response:**
```json
{
  "project_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "in_progress",
  "progress": 0.6,
  "current_phase": "core_development",
  "agents_active": ["developer_001"],
  "last_update": "2024-01-15T10:35:00Z",
  "output_files": [
    "src/App.tsx",
    "server/index.js",
    "package.json"
  ]
}
```

**Status Values:**
- `created`: Project initialized
- `in_progress`: Currently being generated
- `completed`: Successfully completed
- `failed`: Generation failed

### List Projects

Get a list of all projects.

```http
GET /projects
```

**Response:**
```json
[
  {
    "name": "my-todo-app",
    "path": "/path/to/projects/my-todo-app",
    "created": "2024-01-15T10:00:00Z",
    "metadata": {
      "project_id": "550e8400-e29b-41d4-a716-446655440000",
      "status": "completed",
      "type": "fullstack"
    }
  }
]
```

## 🔧 Component Status

### Get Component Status

Check the health and status of all Aetherforge components.

```http
GET /components/status
```

**Response:**
```json
{
  "status": "success",
  "components": {
    "archon": {
      "status": "healthy",
      "service": "Archon",
      "response_time": "0.123s",
      "url": "http://localhost:8100"
    },
    "pheromind": {
      "status": "healthy",
      "service": "Pheromind",
      "response_time": "0.089s"
    },
    "mcp": {
      "status": "unreachable",
      "service": "MCP-Crawl4AI-RAG",
      "error": "Connection refused"
    },
    "bmad": {
      "status": "healthy",
      "service": "BMAD-METHOD"
    }
  },
  "summary": {
    "healthy": 3,
    "total": 4,
    "health_percentage": 75
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 📡 Pheromone System

### Get All Pheromones

Retrieve all pheromone messages from the communication bus.

```http
GET /pheromones
```

**Response:**
```json
{
  "count": 150,
  "pheromones": [
    {
      "id": "pheromone_123",
      "signal": "project_creation_started",
      "payload": {
        "project_id": "550e8400-e29b-41d4-a716-446655440000",
        "project_slug": "my-todo-app"
      },
      "project_id": "550e8400-e29b-41d4-a716-446655440000",
      "agent_id": "orchestrator",
      "trail_id": "project_550e8400",
      "priority": 5,
      "timestamp": "2024-01-15T10:00:00Z"
    }
  ]
}
```

### Drop Pheromone

Send a new pheromone signal to the communication bus.

```http
POST /pheromones
Content-Type: application/json
```

**Request Body:**
```json
{
  "signal": "custom_event",
  "payload": {
    "message": "Custom event occurred",
    "data": "additional_data"
  },
  "project_id": "550e8400-e29b-41d4-a716-446655440000",
  "agent_id": "custom_agent",
  "trail_id": "custom_trail",
  "priority": 5
}
```

**Response:**
```json
{
  "status": "success",
  "pheromone_id": "pheromone_456",
  "signal": "custom_event",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Get Pheromone Statistics

Retrieve real-time statistics about the pheromone system.

```http
GET /pheromones/statistics
```

**Response:**
```json
{
  "status": "success",
  "statistics": {
    "total_messages": 1250,
    "active_projects": 3,
    "active_subscribers": 8,
    "messages_per_minute": 15.5,
    "top_signals": [
      {"signal": "agent_work_completed", "count": 45},
      {"signal": "phase_started", "count": 32}
    ]
  }
}
```

### Get Pheromones by Signal

Filter pheromones by signal type.

```http
GET /pheromones/{signal}
```

**Example:**
```http
GET /pheromones/project_creation_started
```

**Response:**
```json
{
  "signal": "project_creation_started",
  "count": 5,
  "pheromones": [
    {
      "id": "pheromone_123",
      "signal": "project_creation_started",
      "payload": {
        "project_id": "550e8400-e29b-41d4-a716-446655440000"
      },
      "timestamp": "2024-01-15T10:00:00Z"
    }
  ]
}
```

## 🔍 Error Handling

### Error Response Format

All API errors follow this format:

```json
{
  "detail": "Error description",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456"
}
```

### HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

### Common Error Codes

- `INVALID_PROMPT`: Project prompt is invalid or too short
- `PROJECT_NOT_FOUND`: Specified project ID doesn't exist
- `GENERATION_FAILED`: Project generation process failed
- `COMPONENT_UNAVAILABLE`: Required component is not available
- `RATE_LIMIT_EXCEEDED`: Too many requests in time window

## 📊 Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Project Creation**: 5 requests per minute
- **Status Checks**: 60 requests per minute
- **Pheromone Operations**: 100 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: **********
```

## 🔌 WebSocket API

For real-time updates, connect to the WebSocket endpoint:

```javascript
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Real-time update:', data);
};
```

**Message Types:**
- `project_update`: Project status changes
- `pheromone_broadcast`: New pheromone signals
- `component_status`: Component health changes

## 📝 Usage Examples

### Python Example

```python
import requests

# Create a project
response = requests.post('http://localhost:8000/projects', json={
    'prompt': 'Create a blog application with user authentication',
    'project_type': 'fullstack'
})

project_data = response.json()
project_id = project_data['project_id']

# Check project status
status_response = requests.get(f'http://localhost:8000/projects/{project_id}/status')
status = status_response.json()

print(f"Project status: {status['status']}")
print(f"Progress: {status['progress'] * 100}%")
```

### JavaScript Example

```javascript
// Create a project
const createProject = async () => {
  const response = await fetch('http://localhost:8000/projects', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      prompt: 'Create a task management application',
      project_type: 'fullstack'
    })
  });
  
  const project = await response.json();
  return project.project_id;
};

// Monitor project progress
const monitorProject = async (projectId) => {
  const response = await fetch(`http://localhost:8000/projects/${projectId}/status`);
  const status = await response.json();
  
  console.log(`Status: ${status.status}, Progress: ${status.progress * 100}%`);
};
```

### cURL Examples

```bash
# Create project
curl -X POST http://localhost:8000/projects \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a weather dashboard application",
    "project_type": "frontend"
  }'

# Check component status
curl http://localhost:8000/components/status

# Get pheromone statistics
curl http://localhost:8000/pheromones/statistics
```

## 🔄 Versioning

The API uses semantic versioning. Current version: `v1.0.0`

Version information is available in the health check endpoint and all responses include a version header:

```http
X-API-Version: 1.0.0
```

---

**API Reference Complete!** 📚 Use this reference to integrate with Aetherforge programmatically.
