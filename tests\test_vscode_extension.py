"""
Tests for VS Code extension functionality
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock


class TestVSCodeExtensionCore:
    """Test core VS Code extension functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mock_vscode = Mock()
        self.mock_context = Mock()
        self.mock_webview = Mock()
        self.mock_panel = Mock()
        
        # Setup mock VS Code API
        self.mock_vscode.window.createWebviewPanel.return_value = self.mock_panel
        self.mock_panel.webview = self.mock_webview
        self.mock_context.subscriptions = []
    
    @patch('src.aetherforge.vscode', new_callable=lambda: Mock())
    def test_start_aetherforge_creates_webview(self, mock_vscode_module):
        """Test that startAetherforge creates a webview panel"""
        from src.aetherforge import startAetherforge
        
        # Mock the vscode module
        mock_vscode_module.window.createWebviewPanel.return_value = self.mock_panel
        mock_vscode_module.ViewColumn.One = 1
        
        # Call the function
        startAetherforge(self.mock_context)
        
        # Verify webview panel was created
        mock_vscode_module.window.createWebviewPanel.assert_called_once()
        call_args = mock_vscode_module.window.createWebviewPanel.call_args
        
        assert call_args[0][0] == 'aetherforgePanel'
        assert call_args[0][1] == 'Aetherforge'
        assert call_args[0][2] == 1  # ViewColumn.One
        
        # Verify webview options
        options = call_args[1]
        assert options['enableScripts'] is True
        assert options['retainContextWhenHidden'] is True
    
    def test_webview_content_generation(self):
        """Test webview HTML content generation"""
        from src.aetherforge import getWebviewContent
        
        content = getWebviewContent()
        
        # Check that content is valid HTML
        assert content.startswith('<!DOCTYPE html>')
        assert '<html' in content
        assert '</html>' in content
        
        # Check for required elements
        assert 'Aetherforge' in content
        assert 'Create Project' in content
        assert 'System Status' in content
        assert 'Projects' in content
        assert 'Settings' in content
        
        # Check for required form elements
        assert 'id="prompt"' in content
        assert 'id="projectName"' in content
        assert 'id="projectType"' in content
        
        # Check for JavaScript functionality
        assert 'createProject()' in content
        assert 'checkSystemStatus()' in content
        assert 'refreshProjects()' in content
    
    @patch('src.aetherforge.axios')
    def test_create_project_success(self, mock_axios):
        """Test successful project creation"""
        from src.aetherforge import createProject
        
        # Mock successful API response
        mock_response = {
            'data': {
                'status': 'success',
                'project_id': 'test-123',
                'project_slug': 'TestProject',
                'project_path': '/projects/TestProject'
            }
        }
        mock_axios.post.return_value = mock_response
        
        # Call createProject
        createProject(
            'Create a test application',
            self.mock_webview,
            'TestProject',
            'fullstack'
        )
        
        # Verify API was called correctly
        mock_axios.post.assert_called_once()
        call_args = mock_axios.post.call_args
        
        assert 'projects' in call_args[0][0]  # URL contains 'projects'
        
        request_data = call_args[0][1]
        assert request_data['prompt'] == 'Create a test application'
        assert request_data['project_name'] == 'TestProject'
        assert request_data['project_type'] == 'fullstack'
        
        # Verify webview messages
        assert self.mock_webview.postMessage.call_count >= 2
        
        # Check for success message
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        success_messages = [msg for msg in messages if msg.get('command') == 'projectCreated']
        assert len(success_messages) == 1
    
    @patch('src.aetherforge.axios')
    def test_create_project_api_failure(self, mock_axios):
        """Test project creation when API fails"""
        from src.aetherforge import createProject
        
        # Mock API failure
        mock_axios.post.side_effect = Exception('Connection refused')
        
        # Call createProject
        createProject(
            'Create a test application',
            self.mock_webview,
            'TestProject',
            'fullstack'
        )
        
        # Verify error message was sent to webview
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        error_messages = [msg for msg in messages if msg.get('command') == 'error']
        assert len(error_messages) >= 1
    
    @patch('src.aetherforge.axios')
    def test_check_system_status(self, mock_axios):
        """Test system status checking"""
        from src.aetherforge import checkSystemStatus
        
        # Mock API response
        mock_response = {
            'data': {
                'components': {
                    'orchestrator': 'running',
                    'archon': 'offline',
                    'mcp-crawl4ai': 'unknown'
                }
            }
        }
        mock_axios.get.return_value = mock_response
        
        # Call checkSystemStatus
        checkSystemStatus(self.mock_webview)
        
        # Verify API was called
        mock_axios.get.assert_called_once()
        
        # Verify webview received status update
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        status_messages = [msg for msg in messages if msg.get('command') == 'systemStatus']
        assert len(status_messages) == 1
        
        status_data = status_messages[0]['data']
        assert 'components' in status_data
        assert status_data['components']['orchestrator'] == 'running'
    
    @patch('src.aetherforge.axios')
    def test_refresh_projects(self, mock_axios):
        """Test project list refresh"""
        from src.aetherforge import refreshProjects
        
        # Mock API response
        mock_response = {
            'data': [
                {
                    'name': 'Project1',
                    'path': '/projects/Project1',
                    'created': '2023-01-01T00:00:00Z'
                },
                {
                    'name': 'Project2',
                    'path': '/projects/Project2',
                    'created': '2023-01-02T00:00:00Z'
                }
            ]
        }
        mock_axios.get.return_value = mock_response
        
        # Call refreshProjects
        refreshProjects(self.mock_webview)
        
        # Verify API was called
        mock_axios.get.assert_called_once()
        
        # Verify webview received project list
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        project_messages = [msg for msg in messages if msg.get('command') == 'projectsList']
        assert len(project_messages) == 1
        
        projects_data = project_messages[0]['data']
        assert len(projects_data) == 2
        assert projects_data[0]['name'] == 'Project1'
    
    @patch('src.aetherforge.vscode')
    def test_save_settings(self, mock_vscode_module):
        """Test settings save functionality"""
        from src.aetherforge import saveSettings
        
        # Mock workspace configuration
        mock_config = Mock()
        mock_vscode_module.workspace.getConfiguration.return_value = mock_config
        mock_vscode_module.ConfigurationTarget.Workspace = 'workspace'
        
        settings = {
            'orchestratorUrl': 'http://localhost:8000',
            'projectsPath': './projects'
        }
        
        # Call saveSettings
        saveSettings(settings, self.mock_webview)
        
        # Verify configuration was updated
        mock_config.update.assert_called()
        
        # Verify success message was sent
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        success_messages = [msg for msg in messages if 'Settings saved' in msg.get('message', '')]
        assert len(success_messages) >= 1
    
    @patch('src.aetherforge.axios')
    def test_test_connection_success(self, mock_axios):
        """Test successful connection test"""
        from src.aetherforge import testConnection
        
        # Mock successful response
        mock_response = {
            'data': {'status': 'healthy'}
        }
        mock_axios.get.return_value = mock_response
        
        # Call testConnection
        testConnection('http://localhost:8000', self.mock_webview)
        
        # Verify API was called with health endpoint
        mock_axios.get.assert_called_once()
        call_args = mock_axios.get.call_args
        assert '/health' in call_args[0][0]
        
        # Verify success message
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        connection_messages = [msg for msg in messages if msg.get('command') == 'connectionTest']
        assert len(connection_messages) == 1
        assert connection_messages[0]['success'] is True
    
    @patch('src.aetherforge.axios')
    def test_test_connection_failure(self, mock_axios):
        """Test failed connection test"""
        from src.aetherforge import testConnection
        
        # Mock connection failure
        mock_axios.get.side_effect = Exception('Connection failed')
        
        # Call testConnection
        testConnection('http://invalid-url:8000', self.mock_webview)
        
        # Verify failure message
        messages = [call[0][0] for call in self.mock_webview.postMessage.call_args_list]
        connection_messages = [msg for msg in messages if msg.get('command') == 'connectionTest']
        assert len(connection_messages) == 1
        assert connection_messages[0]['success'] is False
        assert 'error' in connection_messages[0]


class TestVSCodeExtensionHelpers:
    """Test helper functions in VS Code extension"""
    
    @patch('src.aetherforge.fs')
    @patch('src.aetherforge.vscode')
    def test_open_projects_folder(self, mock_vscode_module, mock_fs):
        """Test opening projects folder"""
        from src.aetherforge import openProjectsFolder
        
        # Mock file system and VS Code
        mock_fs.existsSync.return_value = True
        mock_vscode_module.workspace.workspaceFolders = [
            Mock(uri=Mock(fsPath='/workspace'))
        ]
        mock_vscode_module.Uri.file.return_value = Mock()
        
        # Call openProjectsFolder
        openProjectsFolder()
        
        # Verify VS Code command was executed
        mock_vscode_module.commands.executeCommand.assert_called_once()
        call_args = mock_vscode_module.commands.executeCommand.call_args
        assert call_args[0][0] == 'vscode.openFolder'
    
    @patch('src.aetherforge.vscode')
    def test_open_project(self, mock_vscode_module):
        """Test opening a specific project"""
        from src.aetherforge import openProject
        
        # Mock VS Code
        mock_vscode_module.Uri.file.return_value = Mock()
        
        # Call openProject
        openProject('/path/to/project')
        
        # Verify VS Code command was executed
        mock_vscode_module.commands.executeCommand.assert_called_once()
        call_args = mock_vscode_module.commands.executeCommand.call_args
        assert call_args[0][0] == 'vscode.openFolder'
    
    @patch('src.aetherforge.axios')
    def test_view_project(self, mock_axios):
        """Test viewing project details"""
        from src.aetherforge import viewProject
        
        # Mock API response
        mock_response = {
            'data': [
                {
                    'name': 'TestProject',
                    'created': '2023-01-01T00:00:00Z',
                    'status': 'completed'
                }
            ]
        }
        mock_axios.get.return_value = mock_response
        
        mock_webview = Mock()
        
        # Call viewProject
        viewProject('TestProject', mock_webview)
        
        # Verify API was called
        mock_axios.get.assert_called_once()
        
        # Verify status message was sent
        messages = [call[0][0] for call in mock_webview.postMessage.call_args_list]
        status_messages = [msg for msg in messages if msg.get('command') == 'updateStatus']
        assert len(status_messages) >= 1


class TestVSCodeExtensionMessageHandling:
    """Test message handling in VS Code extension"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mock_webview = Mock()
        self.mock_context = Mock()
        self.mock_context.subscriptions = []
    
    @patch('src.aetherforge.createProject')
    @patch('src.aetherforge.checkSystemStatus')
    @patch('src.aetherforge.refreshProjects')
    def test_message_routing(self, mock_refresh, mock_status, mock_create):
        """Test that messages are routed to correct handlers"""
        from src.aetherforge import setupWebviewMessageHandling
        
        # Create mock panel
        mock_panel = Mock()
        mock_panel.webview = self.mock_webview
        
        # Setup message handling
        setupWebviewMessageHandling(mock_panel, self.mock_context)
        
        # Get the message handler
        assert self.mock_webview.onDidReceiveMessage.called
        message_handler = self.mock_webview.onDidReceiveMessage.call_args[0][0]
        
        # Test createProject message
        message_handler({
            'command': 'createProject',
            'prompt': 'Test prompt',
            'projectName': 'TestProject',
            'projectType': 'fullstack'
        })
        mock_create.assert_called_once()
        
        # Test checkSystemStatus message
        message_handler({'command': 'checkSystemStatus'})
        mock_status.assert_called_once()
        
        # Test refreshProjects message
        message_handler({'command': 'refreshProjects'})
        mock_refresh.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
