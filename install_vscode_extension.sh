#!/bin/bash

# Aetherforge VS Code Extension Installer
# This script installs the Aetherforge VS Code extension automatically

set -e

echo "🔮 Aetherforge VS Code Extension Installer"
echo "=========================================="

# Check if we're in the right directory
if [ ! -d "vscode-extension" ]; then
    echo "❌ vscode-extension directory not found!"
    echo "Please run this script from the Aetherforge root directory."
    exit 1
fi

# Navigate to extension directory
echo "📁 Navigating to extension directory..."
cd vscode-extension

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found in vscode-extension directory!"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed!"
    echo "Please install npm (usually comes with Node.js)"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Compile TypeScript
echo "🔨 Compiling TypeScript..."
npm run compile

if [ $? -ne 0 ]; then
    echo "❌ Failed to compile extension"
    exit 1
fi

# Install vsce if not already installed
echo "🛠️ Installing VS Code Extension packaging tool..."
if ! command -v vsce &> /dev/null; then
    npm install -g vsce
    
    if [ $? -ne 0 ]; then
        echo "⚠️ Failed to install vsce globally, trying with npx..."
        USE_NPX=true
    fi
fi

# Package extension
echo "📦 Packaging extension..."
if [ "$USE_NPX" = true ]; then
    npx vsce package
else
    vsce package
fi

if [ $? -ne 0 ]; then
    echo "❌ Failed to package extension"
    exit 1
fi

# Check if VS Code is installed
if ! command -v code &> /dev/null; then
    echo "⚠️ VS Code 'code' command not found in PATH"
    echo "Please install VS Code and ensure 'code' command is available"
    echo "Or manually install the .vsix file:"
    echo "  1. Open VS Code"
    echo "  2. Go to Extensions (Ctrl+Shift+X)"
    echo "  3. Click '...' menu"
    echo "  4. Select 'Install from VSIX...'"
    echo "  5. Choose the .vsix file in vscode-extension directory"
    exit 1
fi

# Install in VS Code
echo "🚀 Installing extension in VS Code..."
code --install-extension *.vsix

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Aetherforge VS Code Extension installed successfully!"
    echo ""
    echo "📋 How to use:"
    echo "  1. Open VS Code"
    echo "  2. Press Ctrl+Shift+P (Cmd+Shift+P on Mac)"
    echo "  3. Type 'Aetherforge: Create Project'"
    echo "  4. Use the enhanced UI to create projects!"
    echo ""
    echo "🔧 Extension features:"
    echo "  • Create Project tab - Project creation with advanced options"
    echo "  • System Status tab - Monitor component health"
    echo "  • Projects tab - View and manage generated projects"
    echo "  • Settings tab - Configure orchestrator URL and paths"
    echo ""
    echo "✨ Happy coding with Aetherforge!"
else
    echo "❌ Failed to install extension in VS Code"
    echo "Please try manual installation:"
    echo "  1. Open VS Code"
    echo "  2. Go to Extensions (Ctrl+Shift+X)"
    echo "  3. Click '...' menu"
    echo "  4. Select 'Install from VSIX...'"
    echo "  5. Choose the .vsix file: $(ls *.vsix | head -1)"
fi
