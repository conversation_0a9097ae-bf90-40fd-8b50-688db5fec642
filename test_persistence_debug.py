#!/usr/bin/env python3
"""
Debug Persistence System Issues
"""

import asyncio
import tempfile
import time
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def debug_persistence():
    """Debug persistence system step by step"""
    print("🔍 Debugging Persistence System...")
    
    try:
        from pheromone_bus import PersistenceManager, PersistenceMode, Pheromone, SignalType
        
        print("   ✅ Imports successful")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir)
            print(f"   📁 Using temp directory: {storage_path}")
            
            # Test basic initialization
            try:
                persistence = PersistenceManager(
                    storage_path=storage_path,
                    mode=PersistenceMode.FILE_BASED,
                    max_history_size=100
                )
                print("   ✅ PersistenceManager initialization successful")
            except Exception as e:
                print(f"   ❌ PersistenceManager initialization failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Test pheromone creation
            try:
                pheromone = Pheromone.create(
                    signal=SignalType.PROGRESS,
                    payload={"step": 1, "data": "test_data"},
                    project_id="test_project",
                    agent_id="test_agent"
                )
                print("   ✅ Pheromone creation successful")
                print(f"      Pheromone ID: {pheromone.id}")
                print(f"      Signal: {pheromone.signal}")
                print(f"      Payload: {pheromone.payload}")
            except Exception as e:
                print(f"   ❌ Pheromone creation failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Test persistence
            try:
                success = await persistence.persist_pheromone(pheromone)
                print(f"   ✅ Pheromone persistence: {success}")
                
                if not success:
                    print("   ❌ Persistence returned False")
                    return False
                
                # Check if file was created
                if persistence.history_file.exists():
                    print(f"   ✅ History file created: {persistence.history_file}")

                    # Check file contents (handle both compressed and uncompressed)
                    try:
                        if str(persistence.history_file).endswith('.gz'):
                            import gzip
                            with gzip.open(persistence.history_file, 'rt', encoding='utf-8') as f:
                                content = f.read().strip()
                        else:
                            with open(persistence.history_file, 'r', encoding='utf-8') as f:
                                content = f.read().strip()

                        if content:
                            print(f"   ✅ File has content: {len(content)} characters")
                        else:
                            print("   ⚠️  File is empty")
                    except Exception as e:
                        print(f"   ❌ Error reading file: {e}")
                else:
                    print(f"   ❌ History file not created: {persistence.history_file}")
                
            except Exception as e:
                print(f"   ❌ Pheromone persistence failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Test retrieval
            try:
                pheromones = persistence.get_pheromones()
                print(f"   ✅ Retrieved {len(pheromones)} pheromones")
                
                if len(pheromones) > 0:
                    retrieved = pheromones[0]
                    print(f"      First pheromone ID: {retrieved.id}")
                    print(f"      Matches original: {retrieved.id == pheromone.id}")
                else:
                    print("   ⚠️  No pheromones retrieved")
                
            except Exception as e:
                print(f"   ❌ Pheromone retrieval failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Test filtering
            try:
                filtered = persistence.get_pheromones({"agent_id": "test_agent"})
                print(f"   ✅ Filtered retrieval: {len(filtered)} pheromones")
                
                if len(filtered) > 0 and filtered[0].agent_id == "test_agent":
                    print("   ✅ Filtering working correctly")
                else:
                    print("   ⚠️  Filtering may not be working")
                
            except Exception as e:
                print(f"   ❌ Filtered retrieval failed: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            # Test cleanup
            try:
                persistence.close()
                print("   ✅ Persistence cleanup successful")
            except Exception as e:
                print(f"   ❌ Persistence cleanup failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Debug test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_file_operations():
    """Debug file operations specifically"""
    print("📁 Debugging File Operations...")
    
    try:
        import json
        import gzip
        
        with tempfile.TemporaryDirectory() as temp_dir:
            storage_path = Path(temp_dir)
            history_file = storage_path / "test_history.jsonl"
            
            # Test basic file writing
            test_data = {"id": "test", "signal": "test_signal", "payload": {"test": True}}
            
            try:
                with open(history_file, 'a') as f:
                    f.write(json.dumps(test_data) + '\n')
                print("   ✅ Basic file writing successful")
            except Exception as e:
                print(f"   ❌ Basic file writing failed: {e}")
                return False
            
            # Test file reading
            try:
                with open(history_file, 'r') as f:
                    content = f.read().strip()
                    if content:
                        loaded_data = json.loads(content)
                        print("   ✅ Basic file reading successful")
                        print(f"      Data: {loaded_data}")
                    else:
                        print("   ❌ File is empty after writing")
                        return False
            except Exception as e:
                print(f"   ❌ Basic file reading failed: {e}")
                return False
            
            # Test compression (if enabled)
            try:
                compressed_file = storage_path / "test_compressed.jsonl.gz"
                with gzip.open(compressed_file, 'wt') as f:
                    f.write(json.dumps(test_data) + '\n')
                print("   ✅ Compressed file writing successful")
                
                with gzip.open(compressed_file, 'rt') as f:
                    content = f.read().strip()
                    if content:
                        loaded_data = json.loads(content)
                        print("   ✅ Compressed file reading successful")
                    else:
                        print("   ❌ Compressed file is empty")
                        return False
            except Exception as e:
                print(f"   ❌ Compressed file operations failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ File operations debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def debug_pheromone_serialization():
    """Debug pheromone serialization specifically"""
    print("🔄 Debugging Pheromone Serialization...")
    
    try:
        from pheromone_bus import Pheromone, SignalType, SignalPriority
        import json
        
        # Create test pheromone
        pheromone = Pheromone.create(
            signal=SignalType.COORDINATION,
            payload={"message": "test", "data": [1, 2, 3]},
            project_id="test_project",
            agent_id="test_agent",
            priority=SignalPriority.HIGH,
            tags={"test", "debug"},
            target_agents={"agent1", "agent2"}
        )
        
        print("   ✅ Test pheromone created")
        
        # Test to_dict
        try:
            pheromone_dict = pheromone.to_dict()
            print("   ✅ to_dict() successful")
            print(f"      Keys: {list(pheromone_dict.keys())}")
        except Exception as e:
            print(f"   ❌ to_dict() failed: {e}")
            return False
        
        # Test JSON serialization
        try:
            json_str = json.dumps(pheromone_dict)
            print("   ✅ JSON serialization successful")
            print(f"      JSON length: {len(json_str)} characters")
        except Exception as e:
            print(f"   ❌ JSON serialization failed: {e}")
            return False
        
        # Test JSON deserialization
        try:
            loaded_dict = json.loads(json_str)
            print("   ✅ JSON deserialization successful")
        except Exception as e:
            print(f"   ❌ JSON deserialization failed: {e}")
            return False
        
        # Test from_dict
        try:
            restored_pheromone = Pheromone.from_dict(loaded_dict)
            print("   ✅ from_dict() successful")
            print(f"      Restored ID: {restored_pheromone.id}")
            print(f"      IDs match: {restored_pheromone.id == pheromone.id}")
        except Exception as e:
            print(f"   ❌ from_dict() failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Serialization debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main debug function"""
    print("🔧 Persistence System Debug Suite")
    print("=" * 50)
    
    debug_functions = [
        ("Pheromone Serialization", debug_pheromone_serialization),
        ("File Operations", debug_file_operations),
        ("Persistence System", debug_persistence)
    ]
    
    results = {}
    
    for debug_name, debug_func in debug_functions:
        print()
        results[debug_name] = await debug_func()
    
    # Summary
    print(f"\n📊 Debug Results:")
    print("=" * 30)
    
    for debug_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {debug_name}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\nOverall: {passed}/{total} debug tests passed")
    
    if passed == total:
        print("🎉 All debug tests passed - persistence should be working!")
    else:
        print("🔧 Some issues found - need to fix persistence system")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
