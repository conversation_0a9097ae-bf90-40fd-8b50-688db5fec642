"""
Integration tests for Aetherforge system
"""

import pytest
import asyncio
import time
import requests
import tempfile
import os
import subprocess
import signal
from pathlib import Path
from unittest.mock import patch


class TestSystemIntegration:
    """Test the complete system integration"""
    
    @pytest.fixture(scope="class")
    def orchestrator_server(self):
        """Start orchestrator server for testing"""
        # Start the server in a subprocess
        env = os.environ.copy()
        env['PROJECTS_DIR'] = tempfile.mkdtemp()
        env['PHEROMONE_FILE'] = os.path.join(env['PROJECTS_DIR'], 'test_pheromones.json')
        
        process = subprocess.Popen([
            'uvicorn', 'src.orchestrator:app', 
            '--host', '127.0.0.1', 
            '--port', '8001'  # Use different port for testing
        ], env=env)
        
        # Wait for server to start
        max_attempts = 30
        for _ in range(max_attempts):
            try:
                response = requests.get('http://127.0.0.1:8001/health', timeout=1)
                if response.status_code == 200:
                    break
            except:
                time.sleep(1)
        else:
            process.terminate()
            pytest.fail("Orchestrator server failed to start")
        
        yield process
        
        # Cleanup
        process.terminate()
        process.wait()
    
    def test_full_project_creation_workflow(self, orchestrator_server):
        """Test the complete project creation workflow"""
        base_url = 'http://127.0.0.1:8001'
        
        # 1. Check system health
        response = requests.get(f'{base_url}/health')
        assert response.status_code == 200
        assert response.json()['status'] == 'healthy'
        
        # 2. Check initial pheromone state
        response = requests.get(f'{base_url}/pheromones/statistics')
        assert response.status_code == 200
        initial_stats = response.json()
        
        # 3. Create a project
        project_data = {
            "prompt": "Create a simple calculator web app with basic arithmetic operations",
            "project_name": "SimpleCalculator",
            "project_type": "fullstack"
        }
        
        response = requests.post(f'{base_url}/projects', json=project_data, timeout=60)
        assert response.status_code == 200
        
        project_result = response.json()
        assert project_result['status'] == 'success'
        assert 'project_id' in project_result
        assert 'project_slug' in project_result
        assert project_result['project_slug'] == 'SimpleCalculator'
        
        # 4. Check that pheromones were created during the process
        response = requests.get(f'{base_url}/pheromones/statistics')
        assert response.status_code == 200
        final_stats = response.json()
        
        # Should have more pheromones after project creation
        assert final_stats['total_pheromones'] > initial_stats['total_pheromones']
        
        # 5. Check project list
        response = requests.get(f'{base_url}/projects')
        assert response.status_code == 200
        projects = response.json()
        
        # Should have at least one project
        assert len(projects) >= 1
        
        # Find our project
        our_project = next((p for p in projects if p.get('project_slug') == 'SimpleCalculator'), None)
        assert our_project is not None
        assert our_project['status'] in ['completed', 'in_progress']
    
    def test_component_status_monitoring(self, orchestrator_server):
        """Test component status monitoring"""
        base_url = 'http://127.0.0.1:8001'
        
        response = requests.get(f'{base_url}/components/status')
        assert response.status_code == 200
        
        status_data = response.json()
        assert 'components' in status_data
        assert 'timestamp' in status_data
        
        # Check that we have status for expected components
        components = status_data['components']
        expected_components = ['orchestrator', 'archon', 'mcp-crawl4ai', 'pheromind', 'bmad']
        
        for component in expected_components:
            assert component in components
            # Status should be one of the expected values
            assert components[component] in ['running', 'offline', 'unknown']
    
    def test_pheromone_system_integration(self, orchestrator_server):
        """Test pheromone system integration"""
        base_url = 'http://127.0.0.1:8001'
        
        # Drop a test pheromone
        pheromone_data = {
            "signal": "integration_test_signal",
            "payload": {"test": "data", "timestamp": time.time()},
            "project_id": "integration_test_project"
        }
        
        response = requests.post(f'{base_url}/pheromones', json=pheromone_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result['status'] == 'success'
        assert result['signal'] == 'integration_test_signal'
        
        # Check that the pheromone appears in statistics
        response = requests.get(f'{base_url}/pheromones/statistics')
        assert response.status_code == 200
        
        stats = response.json()
        assert stats['total_pheromones'] > 0
        
        # Check that we can retrieve pheromones
        response = requests.get(f'{base_url}/pheromones')
        assert response.status_code == 200
        
        pheromones = response.json()
        assert len(pheromones) > 0
        
        # Find our test pheromone
        test_pheromone = next((p for p in pheromones if p['signal'] == 'integration_test_signal'), None)
        assert test_pheromone is not None
        assert test_pheromone['project_id'] == 'integration_test_project'
        assert test_pheromone['payload']['test'] == 'data'


class TestErrorScenarios:
    """Test error scenarios and edge cases"""
    
    @pytest.fixture(scope="class")
    def orchestrator_server(self):
        """Start orchestrator server for testing"""
        env = os.environ.copy()
        env['PROJECTS_DIR'] = tempfile.mkdtemp()
        env['PHEROMONE_FILE'] = os.path.join(env['PROJECTS_DIR'], 'test_pheromones.json')
        
        process = subprocess.Popen([
            'uvicorn', 'src.orchestrator:app', 
            '--host', '127.0.0.1', 
            '--port', '8002'  # Use different port
        ], env=env)
        
        # Wait for server to start
        max_attempts = 30
        for _ in range(max_attempts):
            try:
                response = requests.get('http://127.0.0.1:8002/health', timeout=1)
                if response.status_code == 200:
                    break
            except:
                time.sleep(1)
        else:
            process.terminate()
            pytest.fail("Orchestrator server failed to start")
        
        yield process
        
        # Cleanup
        process.terminate()
        process.wait()
    
    def test_invalid_project_requests(self, orchestrator_server):
        """Test handling of invalid project requests"""
        base_url = 'http://127.0.0.1:8002'
        
        # Test empty request
        response = requests.post(f'{base_url}/projects', json={})
        assert response.status_code == 422
        
        # Test missing prompt
        response = requests.post(f'{base_url}/projects', json={
            "project_name": "TestProject",
            "project_type": "fullstack"
        })
        assert response.status_code == 422
        
        # Test invalid project type
        response = requests.post(f'{base_url}/projects', json={
            "prompt": "Create a test app",
            "project_type": "invalid_type"
        })
        assert response.status_code == 422
        
        # Test extremely long prompt
        long_prompt = "Create an app " * 1000  # Very long prompt
        response = requests.post(f'{base_url}/projects', json={
            "prompt": long_prompt,
            "project_type": "fullstack"
        })
        # Should either succeed or fail gracefully
        assert response.status_code in [200, 400, 422, 500]
    
    def test_invalid_pheromone_requests(self, orchestrator_server):
        """Test handling of invalid pheromone requests"""
        base_url = 'http://127.0.0.1:8002'
        
        # Test empty pheromone
        response = requests.post(f'{base_url}/pheromones', json={})
        assert response.status_code == 422
        
        # Test missing signal
        response = requests.post(f'{base_url}/pheromones', json={
            "payload": {"test": "data"},
            "project_id": "test"
        })
        assert response.status_code == 422
        
        # Test missing payload
        response = requests.post(f'{base_url}/pheromones', json={
            "signal": "test_signal",
            "project_id": "test"
        })
        assert response.status_code == 422
    
    def test_concurrent_project_creation(self, orchestrator_server):
        """Test concurrent project creation"""
        base_url = 'http://127.0.0.1:8002'
        
        import threading
        import queue
        
        results = queue.Queue()
        
        def create_project(project_name):
            try:
                project_data = {
                    "prompt": f"Create a simple {project_name} app",
                    "project_name": project_name,
                    "project_type": "fullstack"
                }
                
                response = requests.post(f'{base_url}/projects', json=project_data, timeout=30)
                results.put((project_name, response.status_code, response.json()))
            except Exception as e:
                results.put((project_name, 500, {"error": str(e)}))
        
        # Start multiple concurrent project creations
        threads = []
        project_names = [f"ConcurrentApp{i}" for i in range(3)]
        
        for name in project_names:
            thread = threading.Thread(target=create_project, args=(name,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=60)
        
        # Check results
        successful_projects = 0
        while not results.empty():
            name, status_code, response_data = results.get()
            
            # Should either succeed or fail gracefully
            assert status_code in [200, 400, 422, 500]
            
            if status_code == 200:
                successful_projects += 1
                assert response_data['status'] == 'success'
                assert 'project_id' in response_data
        
        # At least one project should succeed
        assert successful_projects >= 1


class TestPerformance:
    """Test performance characteristics"""
    
    @pytest.fixture(scope="class")
    def orchestrator_server(self):
        """Start orchestrator server for testing"""
        env = os.environ.copy()
        env['PROJECTS_DIR'] = tempfile.mkdtemp()
        env['PHEROMONE_FILE'] = os.path.join(env['PROJECTS_DIR'], 'test_pheromones.json')
        
        process = subprocess.Popen([
            'uvicorn', 'src.orchestrator:app', 
            '--host', '127.0.0.1', 
            '--port', '8003'  # Use different port
        ], env=env)
        
        # Wait for server to start
        max_attempts = 30
        for _ in range(max_attempts):
            try:
                response = requests.get('http://127.0.0.1:8003/health', timeout=1)
                if response.status_code == 200:
                    break
            except:
                time.sleep(1)
        else:
            process.terminate()
            pytest.fail("Orchestrator server failed to start")
        
        yield process
        
        # Cleanup
        process.terminate()
        process.wait()
    
    def test_api_response_times(self, orchestrator_server):
        """Test API response times"""
        base_url = 'http://127.0.0.1:8003'
        
        # Test health endpoint response time
        start_time = time.time()
        response = requests.get(f'{base_url}/health')
        health_time = time.time() - start_time
        
        assert response.status_code == 200
        assert health_time < 1.0  # Should respond within 1 second
        
        # Test pheromone statistics response time
        start_time = time.time()
        response = requests.get(f'{base_url}/pheromones/statistics')
        stats_time = time.time() - start_time
        
        assert response.status_code == 200
        assert stats_time < 2.0  # Should respond within 2 seconds
        
        # Test component status response time
        start_time = time.time()
        response = requests.get(f'{base_url}/components/status')
        status_time = time.time() - start_time
        
        assert response.status_code == 200
        assert status_time < 5.0  # Should respond within 5 seconds
    
    def test_pheromone_throughput(self, orchestrator_server):
        """Test pheromone system throughput"""
        base_url = 'http://127.0.0.1:8003'
        
        # Drop multiple pheromones quickly
        num_pheromones = 50
        start_time = time.time()
        
        for i in range(num_pheromones):
            pheromone_data = {
                "signal": f"performance_test_{i}",
                "payload": {"index": i, "timestamp": time.time()},
                "project_id": "performance_test"
            }
            
            response = requests.post(f'{base_url}/pheromones', json=pheromone_data)
            assert response.status_code == 200
        
        total_time = time.time() - start_time
        throughput = num_pheromones / total_time
        
        # Should be able to handle at least 10 pheromones per second
        assert throughput >= 10.0
        
        # Verify all pheromones were stored
        response = requests.get(f'{base_url}/pheromones/statistics')
        assert response.status_code == 200
        
        stats = response.json()
        assert stats['total_pheromones'] >= num_pheromones


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
