version: '3.8'

services:
  # Aetherforge Orchestrator - Main coordination service
  orchestrator:
    build:
      context: .
      dockerfile: Dockerfile.orchestrator
    environment:
      - ARCHON_URL=http://archon:8100
      - MCP_URL=http://mcp-crawl4ai:8051
      - PHEROMIND_URL=http://pheromind:8502
      - BMAD_URL=http://bmad:8503
      - PROJECTS_DIR=/app/projects
      - PHEROMONE_FILE=/app/pheromones.json
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://aetherforge:${POSTGRES_PASSWORD}@postgres:5432/aetherforge
      - REDIS_URL=redis://redis:6379
    volumes:
      - projects-data:/app/projects
      - pheromone-data:/app/pheromones
    depends_on:
      - redis
      - postgres
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Archon - Agent team generation and coordination
  archon:
    build:
      context: ./components/Archon
      dockerfile: Dockerfile
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ORCHESTRATOR_URL=http://orchestrator:8000
      - PYTHONUNBUFFERED=1
    volumes:
      - archon-data:/app/data
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # MCP-Crawl4AI-RAG - Web crawling and RAG capabilities
  mcp-crawl4ai:
    build:
      context: ./components/mcp-crawl4ai-rag
      dockerfile: Dockerfile
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CRAWL_DEPTH=3
      - MAX_PAGES=100
      - PYTHONUNBUFFERED=1
    volumes:
      - mcp-data:/app/data
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Pheromind - Pheromone-based coordination and visualization
  pheromind:
    build:
      context: ./components/Pheromind
      dockerfile: Dockerfile
    environment:
      - PHEROMONE_BUS_URL=http://orchestrator:8000
      - NODE_ENV=production
      - PYTHONUNBUFFERED=1
    volumes:
      - pheromind-data:/app/data
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # BMAD-METHOD - Development methodology and workflow
  bmad:
    build:
      context: ./components/BMAD-METHOD
      dockerfile: Dockerfile
    environment:
      - ORCHESTRATOR_URL=http://orchestrator:8000
      - WORKFLOW_DIR=/app/workflows
      - PYTHONUNBUFFERED=1
    volumes:
      - bmad-data:/app/workflows
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis - Caching and session storage
  redis:
    image: redis:7-alpine
    volumes:
      - redis-data:/data
    networks:
      - aetherforge-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # PostgreSQL - Primary database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=aetherforge
      - POSTGRES_USER=aetherforge
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Prometheus - Metrics collection
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Grafana - Metrics visualization
  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_SECURITY_DISABLE_GRAVATAR=true
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Nginx - Reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx-logs:/var/log/nginx
    depends_on:
      - orchestrator
      - archon
      - mcp-crawl4ai
      - pheromind
      - bmad
    networks:
      - aetherforge-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  postgres-data:
  redis-data:
  prometheus-data:
  grafana-data:
  projects-data:
  pheromone-data:
  archon-data:
  mcp-data:
  pheromind-data:
  bmad-data:
  nginx-logs:

networks:
  aetherforge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
