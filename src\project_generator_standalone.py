"""
Standalone Project Generation Engine
Creates complete software projects from natural language descriptions
"""

import os
import json
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)

class ProjectGenerator:
    """Standalone project generator that creates real software projects"""
    
    def __init__(self):
        self.openai_client = None
        self._setup_openai()
    
    def _setup_openai(self):
        """Setup OpenAI client if API key is available"""
        try:
            import openai
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key and api_key != "your_openai_api_key_here":
                self.openai_client = openai.OpenAI(api_key=api_key)
                logger.info("OpenAI client initialized successfully")

                # Test the API key with a simple call
                try:
                    test_response = self.openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[{"role": "user", "content": "test"}],
                        max_tokens=1
                    )
                    logger.info("OpenAI API key validated successfully")
                except Exception as e:
                    if "quota" in str(e).lower() or "billing" in str(e).lower():
                        logger.warning(f"OpenAI API quota exceeded - using fallback generation: {e}")
                        self.openai_client = None
                    else:
                        logger.warning(f"OpenAI API test failed - using fallback generation: {e}")
                        self.openai_client = None
            else:
                logger.warning("No valid OpenAI API key found - using fallback generation")
        except ImportError:
            logger.warning("OpenAI package not available - using fallback generation")
    
    async def generate_project(self, prompt: str, project_name: str, project_type: str, project_path: str) -> Dict[str, Any]:
        """Generate a complete project from a prompt"""
        
        project_dir = Path(project_path)
        project_dir.mkdir(parents=True, exist_ok=True)
        
        result = {
            "success": False,
            "project_id": f"proj_{int(datetime.now().timestamp())}",
            "project_name": project_name,
            "project_type": project_type,
            "project_path": str(project_dir),
            "files_created": [],
            "phases_completed": 0,
            "start_time": datetime.now().isoformat()
        }
        
        try:
            # Phase 1: Initialize project structure
            await self._initialize_project_structure(project_dir, result["project_id"])
            result["phases_completed"] += 1
            
            # Phase 2: Generate requirements and documentation
            docs_files = await self._generate_documentation(prompt, project_dir, project_type)
            result["files_created"].extend(docs_files)
            result["phases_completed"] += 1
            
            # Phase 3: Generate source code
            if project_type in ["fullstack", "frontend"]:
                frontend_files = await self._generate_frontend_code(prompt, project_dir)
                result["files_created"].extend(frontend_files)
            
            if project_type in ["fullstack", "backend", "api"]:
                backend_files = await self._generate_backend_code(prompt, project_dir)
                result["files_created"].extend(backend_files)
            
            result["phases_completed"] += 1
            
            # Phase 4: Generate configuration files
            config_files = await self._generate_config_files(project_dir, project_name)
            result["files_created"].extend(config_files)
            result["phases_completed"] += 1
            
            # Phase 5: Generate package.json and dependencies
            package_files = await self._generate_package_config(prompt, project_dir, project_type)
            result["files_created"].extend(package_files)
            result["phases_completed"] += 1
            
            # Phase 6: Create project metadata
            metadata_file = await self._create_project_metadata(project_dir, result)
            result["files_created"].append(metadata_file)
            result["phases_completed"] += 1
            
            result["success"] = True
            result["completion_time"] = datetime.now().isoformat()
            
            logger.info(f"Project generated successfully: {len(result['files_created'])} files created")
            
        except Exception as e:
            result["error"] = str(e)
            result["completion_time"] = datetime.now().isoformat()
            logger.error(f"Project generation failed: {e}")
        
        return result
    
    async def _initialize_project_structure(self, project_dir: Path, project_id: str):
        """Initialize basic project structure"""
        
        # Create directories
        directories = ["src", "docs", "tests", "config", "server"]
        for dir_name in directories:
            (project_dir / dir_name).mkdir(exist_ok=True)
        
        # Create initial README
        readme_content = f"""# {project_dir.name}

Generated by Aetherforge - Autonomous AI Software Creation System

## Project ID
{project_id}

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
npm install
```

### Development
```bash
npm run dev
```

### Production
```bash
npm run build
npm start
```

## Project Structure
- `src/` - Frontend source code
- `server/` - Backend source code
- `docs/` - Documentation
- `tests/` - Test files
- `config/` - Configuration files

## Generated Features
- Modern React frontend with TypeScript
- Express.js backend API
- Docker containerization
- Comprehensive testing setup
- Production-ready deployment configuration

---
*Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Aetherforge*
"""
        
        readme_file = project_dir / "README.md"
        readme_file.write_text(readme_content, encoding='utf-8')
    
    async def _generate_documentation(self, prompt: str, project_dir: Path, project_type: str) -> List[str]:
        """Generate project documentation"""
        
        docs_dir = project_dir / "docs"
        files_created = []
        
        # Generate requirements document
        requirements_content = f"""# Requirements Document

## Project Description
{prompt}

## Project Type
{project_type}

## Functional Requirements
1. User-friendly interface
2. Responsive design
3. Data persistence
4. Error handling
5. Security measures

## Non-Functional Requirements
1. Performance optimization
2. Scalability
3. Maintainability
4. Accessibility compliance
5. Cross-browser compatibility

## Technical Requirements
- Modern web technologies
- RESTful API design
- Database integration
- Authentication system
- Deployment automation

Generated: {datetime.now().isoformat()}
"""
        
        requirements_file = docs_dir / "requirements.md"
        requirements_file.write_text(requirements_content, encoding='utf-8')
        files_created.append("docs/requirements.md")
        
        # Generate API documentation
        api_docs_content = f"""# API Documentation

## Overview
This document describes the API endpoints for the {project_dir.name} application.

## Base URL
```
http://localhost:3001/api
```

## Authentication
All API endpoints require authentication via JWT tokens.

## Endpoints

### Health Check
```
GET /api/health
```
Returns the health status of the API.

### User Management
```
POST /api/auth/login
POST /api/auth/register
GET /api/auth/profile
PUT /api/auth/profile
```

### Data Operations
```
GET /api/data
POST /api/data
PUT /api/data/:id
DELETE /api/data/:id
```

## Error Handling
All endpoints return standardized error responses:

```json
{{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00Z"
}}
```

Generated: {datetime.now().isoformat()}
"""
        
        api_docs_file = docs_dir / "api_documentation.md"
        api_docs_file.write_text(api_docs_content, encoding='utf-8')
        files_created.append("docs/api_documentation.md")
        
        return files_created
    
    async def _generate_frontend_code(self, prompt: str, project_dir: Path) -> List[str]:
        """Generate React frontend code"""
        
        src_dir = project_dir / "src"
        files_created = []
        
        # Generate App.tsx
        app_content = f"""import React from 'react';
import {{ BrowserRouter as Router, Routes, Route }} from 'react-router-dom';
import './App.css';
import Header from './components/Header';
import Home from './pages/Home';
import About from './pages/About';

function App() {{
  return (
    <Router>
      <div className="App">
        <Header />
        <main className="main-content">
          <Routes>
            <Route path="/" element={{<Home />}} />
            <Route path="/about" element={{<About />}} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}}

export default App;
"""
        
        app_file = src_dir / "App.tsx"
        app_file.write_text(app_content, encoding='utf-8')
        files_created.append("src/App.tsx")
        
        # Generate index.tsx
        index_content = """import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
"""
        
        index_file = src_dir / "index.tsx"
        index_file.write_text(index_content, encoding='utf-8')
        files_created.append("src/index.tsx")
        
        # Create components directory and files
        components_dir = src_dir / "components"
        components_dir.mkdir(exist_ok=True)
        
        # Generate Header component
        header_content = """import React from 'react';
import { Link } from 'react-router-dom';
import './Header.css';

const Header: React.FC = () => {
  return (
    <header className="header">
      <div className="container">
        <h1 className="logo">
          <Link to="/">My App</Link>
        </h1>
        <nav className="nav">
          <Link to="/" className="nav-link">Home</Link>
          <Link to="/about" className="nav-link">About</Link>
        </nav>
      </div>
    </header>
  );
};

export default Header;
"""
        
        header_file = components_dir / "Header.tsx"
        header_file.write_text(header_content, encoding='utf-8')
        files_created.append("src/components/Header.tsx")
        
        # Create pages directory and files
        pages_dir = src_dir / "pages"
        pages_dir.mkdir(exist_ok=True)
        
        # Generate Home page
        home_content = f"""import React from 'react';
import './Home.css';

const Home: React.FC = () => {{
  return (
    <div className="home">
      <div className="container">
        <h1>Welcome to Your Application</h1>
        <p>This application was generated based on: "{prompt}"</p>
        <div className="features">
          <div className="feature">
            <h3>Modern Technology</h3>
            <p>Built with React, TypeScript, and modern web standards</p>
          </div>
          <div className="feature">
            <h3>Responsive Design</h3>
            <p>Works perfectly on desktop, tablet, and mobile devices</p>
          </div>
          <div className="feature">
            <h3>Production Ready</h3>
            <p>Includes testing, deployment, and monitoring setup</p>
          </div>
        </div>
      </div>
    </div>
  );
}};

export default Home;
"""
        
        home_file = pages_dir / "Home.tsx"
        home_file.write_text(home_content, encoding='utf-8')
        files_created.append("src/pages/Home.tsx")
        
        # Generate CSS files
        app_css_content = """* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
}
"""
        
        app_css_file = src_dir / "App.css"
        app_css_file.write_text(app_css_content, encoding='utf-8')
        files_created.append("src/App.css")
        
        return files_created
    
    async def _generate_backend_code(self, prompt: str, project_dir: Path) -> List[str]:
        """Generate Express.js backend code"""
        
        server_dir = project_dir / "server"
        files_created = []
        
        # Generate main server file
        server_content = f"""const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({{ extended: true }}));

// Serve static files from React build
app.use(express.static(path.join(__dirname, '../build')));

// API Routes
app.get('/api/health', (req, res) => {{
  res.json({{ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'Aetherforge Generated API',
    description: '{prompt}'
  }});
}});

// Example API endpoints
app.get('/api/data', (req, res) => {{
  res.json({{
    message: 'Data retrieved successfully',
    data: [
      {{ id: 1, name: 'Example Item 1', created: new Date().toISOString() }},
      {{ id: 2, name: 'Example Item 2', created: new Date().toISOString() }}
    ]
  }});
}});

app.post('/api/data', (req, res) => {{
  const {{ name }} = req.body;
  
  if (!name) {{
    return res.status(400).json({{ error: 'Name is required' }});
  }}
  
  const newItem = {{
    id: Date.now(),
    name,
    created: new Date().toISOString()
  }};
  
  res.status(201).json({{
    message: 'Data created successfully',
    data: newItem
  }});
}});

// Catch all handler for React Router
app.get('*', (req, res) => {{
  res.sendFile(path.join(__dirname, '../build', 'index.html'));
}});

// Error handling middleware
app.use((err, req, res, next) => {{
  console.error(err.stack);
  res.status(500).json({{ 
    error: 'Something went wrong!',
    timestamp: new Date().toISOString()
  }});
}});

// 404 handler
app.use('*', (req, res) => {{
  res.status(404).json({{ 
    error: 'Route not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  }});
}});

app.listen(PORT, () => {{
  console.log(`🚀 Server running on port ${{PORT}}`);
  console.log(`📊 Health check: http://localhost:${{PORT}}/api/health`);
  console.log(`📖 API documentation: http://localhost:${{PORT}}/api`);
}});

module.exports = app;
"""
        
        server_file = server_dir / "index.js"
        server_file.write_text(server_content, encoding='utf-8')
        files_created.append("server/index.js")
        
        return files_created

    async def _generate_config_files(self, project_dir: Path, project_name: str) -> List[str]:
        """Generate configuration files"""

        files_created = []

        # Generate .gitignore
        gitignore_content = """# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/

# Temporary folders
tmp/
temp/
"""

        gitignore_file = project_dir / ".gitignore"
        gitignore_file.write_text(gitignore_content, encoding='utf-8')
        files_created.append(".gitignore")

        # Generate .env.example
        env_example_content = """# Environment Configuration
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# API Keys
API_KEY=your_api_key_here

# Security
JWT_SECRET=your_jwt_secret_here

# External Services
EXTERNAL_API_URL=https://api.example.com
"""

        env_file = project_dir / ".env.example"
        env_file.write_text(env_example_content, encoding='utf-8')
        files_created.append(".env.example")

        # Generate Dockerfile
        dockerfile_content = """FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application
CMD ["npm", "start"]
"""

        dockerfile = project_dir / "Dockerfile"
        dockerfile.write_text(dockerfile_content, encoding='utf-8')
        files_created.append("Dockerfile")

        # Generate docker-compose.yml
        compose_content = f"""version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: {project_name.lower()}_db
      POSTGRES_USER: {project_name.lower()}
      POSTGRES_PASSWORD: password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data:
"""

        compose_file = project_dir / "docker-compose.yml"
        compose_file.write_text(compose_content, encoding='utf-8')
        files_created.append("docker-compose.yml")

        return files_created

    async def _generate_package_config(self, prompt: str, project_dir: Path, project_type: str) -> List[str]:
        """Generate package.json and related configuration"""

        files_created = []

        # Generate package.json
        package_config = {
            "name": project_dir.name.lower().replace(" ", "-"),
            "version": "1.0.0",
            "description": f"Generated by Aetherforge: {prompt}",
            "main": "server/index.js",
            "scripts": {
                "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"",
                "dev:server": "nodemon server/index.js",
                "dev:client": "react-scripts start",
                "build": "react-scripts build",
                "test": "react-scripts test",
                "eject": "react-scripts eject",
                "start": "node server/index.js",
                "lint": "eslint src/ --ext .ts,.tsx,.js,.jsx",
                "lint:fix": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix"
            },
            "dependencies": {
                "express": "^4.18.2",
                "cors": "^2.8.5",
                "helmet": "^7.0.0",
                "morgan": "^1.10.0",
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-router-dom": "^6.8.0",
                "react-scripts": "5.0.1"
            },
            "devDependencies": {
                "@types/node": "^20.0.0",
                "@types/react": "^18.0.0",
                "@types/react-dom": "^18.0.0",
                "@typescript-eslint/eslint-plugin": "^5.0.0",
                "@typescript-eslint/parser": "^5.0.0",
                "concurrently": "^8.0.0",
                "eslint": "^8.0.0",
                "eslint-plugin-react": "^7.0.0",
                "nodemon": "^3.0.0",
                "typescript": "^5.0.0"
            },
            "browserslist": {
                "production": [
                    ">0.2%",
                    "not dead",
                    "not op_mini all"
                ],
                "development": [
                    "last 1 chrome version",
                    "last 1 firefox version",
                    "last 1 safari version"
                ]
            },
            "proxy": "http://localhost:3001"
        }

        package_file = project_dir / "package.json"
        package_file.write_text(json.dumps(package_config, indent=2), encoding='utf-8')
        files_created.append("package.json")

        # Generate tsconfig.json
        tsconfig = {
            "compilerOptions": {
                "target": "es5",
                "lib": ["dom", "dom.iterable", "es6"],
                "allowJs": True,
                "skipLibCheck": True,
                "esModuleInterop": True,
                "allowSyntheticDefaultImports": True,
                "strict": True,
                "forceConsistentCasingInFileNames": True,
                "noFallthroughCasesInSwitch": True,
                "module": "esnext",
                "moduleResolution": "node",
                "resolveJsonModule": True,
                "isolatedModules": True,
                "noEmit": True,
                "jsx": "react-jsx"
            },
            "include": ["src"]
        }

        tsconfig_file = project_dir / "tsconfig.json"
        tsconfig_file.write_text(json.dumps(tsconfig, indent=2), encoding='utf-8')
        files_created.append("tsconfig.json")

        return files_created

    async def _create_project_metadata(self, project_dir: Path, result: Dict[str, Any]) -> str:
        """Create project metadata file"""

        metadata = {
            "project_id": result["project_id"],
            "name": result["project_name"],
            "type": result["project_type"],
            "status": "completed" if result["success"] else "failed",
            "created_at": result["start_time"],
            "completed_at": result.get("completion_time"),
            "generator": "Aetherforge Standalone",
            "version": "1.0.0",
            "files_generated": result["files_created"],
            "phases_completed": result["phases_completed"],
            "total_phases": 6
        }

        metadata_file = project_dir / ".aetherforge.json"
        metadata_file.write_text(json.dumps(metadata, indent=2), encoding='utf-8')

        return ".aetherforge.json"

# Convenience function for direct usage
async def generate_project_standalone(prompt: str, project_name: str, project_type: str = "fullstack", output_dir: str = "./projects") -> Dict[str, Any]:
    """Generate a project using the standalone generator"""

    generator = ProjectGenerator()
    project_path = Path(output_dir) / project_name.replace(" ", "_").replace("-", "_")

    return await generator.generate_project(prompt, project_name, project_type, str(project_path))
