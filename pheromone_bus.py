#!/usr/bin/env python3
"""
Robust Pheromone Bus - Complete Pheromind Communication Protocol Implementation

This module implements a comprehensive pheromone-based communication system
for agent coordination with signal persistence, history tracking, and
subscription mechanisms.

Features:
- Signal persistence with file-based and in-memory storage
- Agent subscription system with filtering and real-time notifications
- History tracking with configurable retention policies
- Signal decay and priority handling
- Pattern matching and analytics
- Thread-safe operations with async support
- Comprehensive error handling and recovery
- Performance monitoring and statistics
"""

import asyncio
import json
import time
import uuid
import threading
import weakref
import re
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Set, Union, Tuple
from dataclasses import dataclass, asdict, field
from enum import Enum, IntEnum
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor
import sqlite3
import pickle
import gzip

# Configure logging
logger = logging.getLogger(__name__)

class SignalType(str, Enum):
    """Pheromone signal types for different communication purposes"""
    # Core coordination signals
    COORDINATION = "coordination"
    PROGRESS = "progress"
    ERROR = "error"
    COMPLETION = "completion"

    # Agent lifecycle signals
    AGENT_START = "agent_start"
    AGENT_STOP = "agent_stop"
    AGENT_HEARTBEAT = "agent_heartbeat"
    AGENT_STATUS = "agent_status"

    # Project lifecycle signals
    PROJECT_START = "project_start"
    PROJECT_PHASE = "project_phase"
    PROJECT_MILESTONE = "project_milestone"
    PROJECT_END = "project_end"

    # Workflow signals
    WORKFLOW_START = "workflow_start"
    WORKFLOW_STEP = "workflow_step"
    WORKFLOW_DECISION = "workflow_decision"
    WORKFLOW_END = "workflow_end"

    # Resource signals
    RESOURCE_REQUEST = "resource_request"
    RESOURCE_AVAILABLE = "resource_available"
    RESOURCE_LOCK = "resource_lock"
    RESOURCE_RELEASE = "resource_release"

    # Communication signals
    MESSAGE = "message"
    BROADCAST = "broadcast"
    NOTIFICATION = "notification"
    ALERT = "alert"

    # System signals
    SYSTEM_STATUS = "system_status"
    HEALTH_CHECK = "health_check"
    PERFORMANCE = "performance"

    # Custom signals
    CUSTOM = "custom"

class SignalPriority(IntEnum):
    """Signal priority levels for processing order"""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    BACKGROUND = 5

class PersistenceMode(str, Enum):
    """Persistence storage modes"""
    MEMORY_ONLY = "memory_only"
    FILE_BASED = "file_based"
    DATABASE = "database"
    HYBRID = "hybrid"

@dataclass
class Pheromone:
    """Enhanced pheromone data structure with comprehensive metadata"""
    id: str
    signal: SignalType
    payload: Dict[str, Any]
    timestamp: float
    priority: SignalPriority = SignalPriority.NORMAL

    # Agent and project context
    agent_id: Optional[str] = None
    project_id: Optional[str] = None
    trail_id: Optional[str] = None

    # Signal metadata
    ttl: Optional[float] = None  # Time to live in seconds
    decay_rate: float = 0.0  # Signal strength decay per second
    strength: float = 1.0  # Current signal strength (0.0 to 1.0)

    # Routing and filtering
    tags: Set[str] = field(default_factory=set)
    target_agents: Set[str] = field(default_factory=set)
    source_agent: Optional[str] = None

    # Persistence metadata
    persisted: bool = False
    version: int = 1

    # Performance tracking
    processing_time: Optional[float] = None
    retry_count: int = 0

    def __post_init__(self):
        """Initialize computed fields"""
        if isinstance(self.signal, str):
            self.signal = SignalType(self.signal)
        if isinstance(self.priority, int):
            self.priority = SignalPriority(self.priority)

    def is_expired(self) -> bool:
        """Check if pheromone has expired based on TTL"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl

    def current_strength(self) -> float:
        """Calculate current signal strength with decay"""
        if self.decay_rate <= 0:
            return self.strength

        elapsed = time.time() - self.timestamp
        decayed_strength = self.strength * (1 - self.decay_rate * elapsed)
        return max(0.0, decayed_strength)

    def age_seconds(self) -> float:
        """Get age of pheromone in seconds"""
        return time.time() - self.timestamp

    def matches_filter(self, filter_criteria: Dict[str, Any]) -> bool:
        """Check if pheromone matches filter criteria"""
        for key, value in filter_criteria.items():
            if key == "signal" and self.signal != value:
                return False
            elif key == "agent_id" and self.agent_id != value:
                return False
            elif key == "project_id" and self.project_id != value:
                return False
            elif key == "trail_id" and self.trail_id != value:
                return False
            elif key == "priority" and self.priority != value:
                return False
            elif key == "tags" and not self.tags.intersection(set(value)):
                return False
            elif key == "min_strength" and self.current_strength() < value:
                return False
            elif key == "max_age" and self.age_seconds() > value:
                return False
            elif key == "pattern" and not re.search(value, json.dumps(self.payload)):
                return False
            elif key == "source_agent" and self.source_agent != value:
                return False
            elif key == "target_agents" and not self.target_agents.intersection(set(value)):
                return False
        return True

    def add_tag(self, tag: str) -> None:
        """Add a tag to the pheromone"""
        self.tags.add(tag)

    def add_target_agent(self, agent_id: str) -> None:
        """Add a target agent"""
        self.target_agents.add(agent_id)

    def to_dict(self) -> Dict[str, Any]:
        """Convert pheromone to dictionary for serialization"""
        data = asdict(self)
        data['tags'] = list(self.tags)
        data['target_agents'] = list(self.target_agents)
        data['signal'] = self.signal.value
        data['priority'] = self.priority.value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Pheromone':
        """Create pheromone from dictionary"""
        # Convert enum values back
        data['signal'] = SignalType(data['signal'])
        data['priority'] = SignalPriority(data['priority'])
        data['tags'] = set(data.get('tags', []))
        data['target_agents'] = set(data.get('target_agents', []))
        return cls(**data)

    @classmethod
    def create(
        cls,
        signal: Union[str, SignalType],
        payload: Dict[str, Any],
        project_id: str = None,
        agent_id: str = None,
        trail_id: str = None,
        priority: Union[int, SignalPriority] = SignalPriority.NORMAL,
        ttl: float = None,
        tags: Set[str] = None,
        target_agents: Set[str] = None
    ) -> 'Pheromone':
        """Factory method to create a pheromone with sensible defaults"""
        return cls(
            id=str(uuid.uuid4()),
            signal=SignalType(signal) if isinstance(signal, str) else signal,
            payload=payload,
            timestamp=time.time(),
            priority=SignalPriority(priority) if isinstance(priority, int) else priority,
            project_id=project_id,
            agent_id=agent_id,
            trail_id=trail_id,
            ttl=ttl,
            tags=tags or set(),
            target_agents=target_agents or set()
        )


@dataclass
class PheromoneTrail:
    """Enhanced collection of related pheromones forming a trail"""
    id: str
    name: str
    description: str
    project_id: Optional[str] = None
    agent_id: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Trail configuration
    max_pheromones: int = 1000
    auto_cleanup: bool = True
    retention_hours: int = 24

    # Internal storage
    _pheromones: List[str] = field(default_factory=list)  # Store pheromone IDs
    _pheromone_cache: Dict[str, Pheromone] = field(default_factory=dict, init=False)

    def add_pheromone(self, pheromone: Pheromone) -> None:
        """Add pheromone to trail"""
        self._pheromones.append(pheromone.id)
        self._pheromone_cache[pheromone.id] = pheromone
        self.updated_at = time.time()

        # Auto-cleanup if enabled
        if self.auto_cleanup and len(self._pheromones) > self.max_pheromones:
            self._cleanup_oldest()

    def _cleanup_oldest(self) -> None:
        """Remove oldest pheromones to maintain size limit"""
        while len(self._pheromones) > self.max_pheromones:
            oldest_id = self._pheromones.pop(0)
            self._pheromone_cache.pop(oldest_id, None)

    def get_pheromones(
        self,
        limit: int = None,
        since: float = None,
        signal_type: SignalType = None,
        agent_id: str = None
    ) -> List[Pheromone]:
        """Get pheromones with optional filtering"""
        pheromones = []

        for pheromone_id in reversed(self._pheromones):  # Newest first
            if pheromone_id in self._pheromone_cache:
                pheromone = self._pheromone_cache[pheromone_id]

                # Apply filters
                if since and pheromone.timestamp < since:
                    continue
                if signal_type and pheromone.signal != signal_type:
                    continue
                if agent_id and pheromone.agent_id != agent_id:
                    continue

                pheromones.append(pheromone)

                # Apply limit
                if limit and len(pheromones) >= limit:
                    break

        return pheromones

    def get_recent_pheromones(self, minutes: int = 30) -> List[Pheromone]:
        """Get pheromones from the last N minutes"""
        cutoff_time = time.time() - (minutes * 60)
        return self.get_pheromones(since=cutoff_time)

    def get_pheromones_by_signal(self, signal: SignalType) -> List[Pheromone]:
        """Get all pheromones of a specific signal type"""
        return self.get_pheromones(signal_type=signal)

    def get_pheromones_by_agent(self, agent_id: str) -> List[Pheromone]:
        """Get all pheromones from a specific agent"""
        return self.get_pheromones(agent_id=agent_id)

    def cleanup_expired(self) -> int:
        """Remove expired pheromones and return count removed"""
        cutoff_time = time.time() - (self.retention_hours * 3600)
        original_count = len(self._pheromones)

        # Filter out expired pheromones
        valid_ids = []
        for pheromone_id in self._pheromones:
            if pheromone_id in self._pheromone_cache:
                pheromone = self._pheromone_cache[pheromone_id]
                if pheromone.timestamp >= cutoff_time and not pheromone.is_expired():
                    valid_ids.append(pheromone_id)
                else:
                    self._pheromone_cache.pop(pheromone_id, None)

        self._pheromones = valid_ids
        removed_count = original_count - len(self._pheromones)

        if removed_count > 0:
            self.updated_at = time.time()

        return removed_count

    def get_statistics(self) -> Dict[str, Any]:
        """Get trail statistics"""
        pheromones = list(self._pheromone_cache.values())

        if not pheromones:
            return {
                "total_pheromones": 0,
                "signal_types": {},
                "agents": {},
                "age_range": None,
                "average_strength": 0.0
            }

        # Count by signal type
        signal_counts = defaultdict(int)
        agent_counts = defaultdict(int)
        strengths = []

        for pheromone in pheromones:
            signal_counts[pheromone.signal.value] += 1
            if pheromone.agent_id:
                agent_counts[pheromone.agent_id] += 1
            strengths.append(pheromone.current_strength())

        timestamps = [p.timestamp for p in pheromones]

        return {
            "total_pheromones": len(pheromones),
            "signal_types": dict(signal_counts),
            "agents": dict(agent_counts),
            "age_range": {
                "oldest": min(timestamps),
                "newest": max(timestamps),
                "span_hours": (max(timestamps) - min(timestamps)) / 3600
            },
            "average_strength": sum(strengths) / len(strengths) if strengths else 0.0,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert trail to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "project_id": self.project_id,
            "agent_id": self.agent_id,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "metadata": self.metadata,
            "max_pheromones": self.max_pheromones,
            "auto_cleanup": self.auto_cleanup,
            "retention_hours": self.retention_hours,
            "pheromone_ids": self._pheromones
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PheromoneTrail':
        """Create trail from dictionary"""
        pheromone_ids = data.pop('pheromone_ids', [])
        trail = cls(**data)
        trail._pheromones = pheromone_ids
        return trail


class AgentSubscription:
    """Enhanced agent subscription to pheromone signals with advanced filtering"""

    def __init__(
        self,
        agent_id: str,
        callback: Callable[[Pheromone], Any],
        filter_criteria: Dict[str, Any] = None,
        real_time: bool = True,
        batch_size: int = 1,
        max_queue_size: int = 1000
    ):
        self.agent_id = agent_id
        self.callback = callback
        self.filter_criteria = filter_criteria or {}
        self.real_time = real_time
        self.batch_size = batch_size
        self.max_queue_size = max_queue_size

        # State tracking
        self.created_at = time.time()
        self.last_notification = 0.0
        self.notification_count = 0
        self.error_count = 0
        self.active = True

        # Queue for batching
        self.queue = asyncio.Queue(maxsize=max_queue_size)
        self.batch_buffer = []
        self.last_batch_time = time.time()

        # Performance metrics
        self.total_processing_time = 0.0
        self.avg_processing_time = 0.0

    def matches(self, pheromone: Pheromone) -> bool:
        """Check if pheromone matches subscription criteria"""
        if not self.active:
            return False
        return pheromone.matches_filter(self.filter_criteria)

    async def notify(self, pheromone: Pheromone) -> bool:
        """Notify subscriber about matching pheromone"""
        if not self.matches(pheromone):
            return False

        start_time = time.time()

        try:
            if self.batch_size > 1:
                await self._handle_batched_notification(pheromone)
            else:
                await self._handle_immediate_notification(pheromone)

            # Update metrics
            processing_time = time.time() - start_time
            self.total_processing_time += processing_time
            self.notification_count += 1
            self.avg_processing_time = self.total_processing_time / self.notification_count
            self.last_notification = time.time()

            return True

        except Exception as e:
            self.error_count += 1
            logger.error(f"Error notifying subscriber {self.agent_id}: {e}")
            return False

    async def _handle_immediate_notification(self, pheromone: Pheromone) -> None:
        """Handle immediate notification"""
        if asyncio.iscoroutinefunction(self.callback):
            await self.callback(pheromone)
        else:
            self.callback(pheromone)

    async def _handle_batched_notification(self, pheromone: Pheromone) -> None:
        """Handle batched notification"""
        self.batch_buffer.append(pheromone)

        # Send batch if full or timeout reached
        if (len(self.batch_buffer) >= self.batch_size or
            time.time() - self.last_batch_time > 1.0):  # 1 second timeout

            batch = self.batch_buffer.copy()
            self.batch_buffer.clear()
            self.last_batch_time = time.time()

            if asyncio.iscoroutinefunction(self.callback):
                await self.callback(batch)
            else:
                self.callback(batch)

    async def flush_batch(self) -> None:
        """Flush any pending batched notifications"""
        if self.batch_buffer:
            batch = self.batch_buffer.copy()
            self.batch_buffer.clear()
            self.last_batch_time = time.time()

            if asyncio.iscoroutinefunction(self.callback):
                await self.callback(batch)
            else:
                self.callback(batch)

    def get_statistics(self) -> Dict[str, Any]:
        """Get subscription statistics"""
        return {
            "agent_id": self.agent_id,
            "active": self.active,
            "created_at": self.created_at,
            "notification_count": self.notification_count,
            "error_count": self.error_count,
            "last_notification": self.last_notification,
            "avg_processing_time": self.avg_processing_time,
            "filter_criteria": self.filter_criteria,
            "real_time": self.real_time,
            "batch_size": self.batch_size,
            "pending_batch_size": len(self.batch_buffer)
        }

    def update_filter(self, new_criteria: Dict[str, Any]) -> None:
        """Update filter criteria"""
        self.filter_criteria = new_criteria

    def deactivate(self) -> None:
        """Deactivate subscription"""
        self.active = False

class SubscriptionManager:
    """Manages agent subscriptions with advanced features"""

    def __init__(self):
        self.subscriptions: Dict[str, AgentSubscription] = {}
        self.agent_subscriptions: Dict[str, Set[str]] = defaultdict(set)  # agent_id -> subscription_ids
        self.signal_subscriptions: Dict[SignalType, Set[str]] = defaultdict(set)  # signal -> subscription_ids
        self.project_subscriptions: Dict[str, Set[str]] = defaultdict(set)  # project_id -> subscription_ids

        # Performance tracking
        self.total_notifications = 0
        self.failed_notifications = 0
        self.last_cleanup = time.time()

    def subscribe(
        self,
        agent_id: str,
        callback: Callable[[Pheromone], Any],
        filter_criteria: Dict[str, Any] = None,
        real_time: bool = True,
        batch_size: int = 1,
        subscription_id: str = None
    ) -> str:
        """Create a new subscription"""
        if subscription_id is None:
            subscription_id = f"{agent_id}_{uuid.uuid4().hex[:8]}"

        subscription = AgentSubscription(
            agent_id=agent_id,
            callback=callback,
            filter_criteria=filter_criteria,
            real_time=real_time,
            batch_size=batch_size
        )

        self.subscriptions[subscription_id] = subscription
        self.agent_subscriptions[agent_id].add(subscription_id)

        # Index by signal type for faster lookup
        if filter_criteria and "signal" in filter_criteria:
            signal = filter_criteria["signal"]
            if isinstance(signal, str):
                signal = SignalType(signal)
            self.signal_subscriptions[signal].add(subscription_id)

        # Index by project for faster lookup
        if filter_criteria and "project_id" in filter_criteria:
            project_id = filter_criteria["project_id"]
            self.project_subscriptions[project_id].add(subscription_id)

        logger.info(f"Created subscription {subscription_id} for agent {agent_id}")
        return subscription_id

    def unsubscribe(self, subscription_id: str) -> bool:
        """Remove a subscription"""
        if subscription_id not in self.subscriptions:
            return False

        subscription = self.subscriptions[subscription_id]
        agent_id = subscription.agent_id

        # Remove from all indexes
        self.agent_subscriptions[agent_id].discard(subscription_id)

        for signal_subs in self.signal_subscriptions.values():
            signal_subs.discard(subscription_id)

        for project_subs in self.project_subscriptions.values():
            project_subs.discard(subscription_id)

        # Deactivate and remove
        subscription.deactivate()
        del self.subscriptions[subscription_id]

        logger.info(f"Removed subscription {subscription_id}")
        return True

    def unsubscribe_agent(self, agent_id: str) -> int:
        """Remove all subscriptions for an agent"""
        subscription_ids = list(self.agent_subscriptions.get(agent_id, set()))
        count = 0

        for subscription_id in subscription_ids:
            if self.unsubscribe(subscription_id):
                count += 1

        return count

    async def notify_subscribers(self, pheromone: Pheromone) -> Tuple[int, int]:
        """Notify all matching subscribers"""
        notified = 0
        failed = 0

        # Get potentially matching subscriptions
        candidate_subscriptions = set()

        # Add subscriptions by signal type
        if pheromone.signal in self.signal_subscriptions:
            candidate_subscriptions.update(self.signal_subscriptions[pheromone.signal])

        # Add subscriptions by project
        if pheromone.project_id and pheromone.project_id in self.project_subscriptions:
            candidate_subscriptions.update(self.project_subscriptions[pheromone.project_id])

        # If no specific matches, check all subscriptions
        if not candidate_subscriptions:
            candidate_subscriptions = set(self.subscriptions.keys())

        # Notify matching subscriptions
        for subscription_id in candidate_subscriptions:
            if subscription_id in self.subscriptions:
                subscription = self.subscriptions[subscription_id]
                try:
                    success = await subscription.notify(pheromone)
                    if success:
                        notified += 1
                    else:
                        failed += 1
                except Exception as e:
                    failed += 1
                    logger.error(f"Error in subscription {subscription_id}: {e}")

        self.total_notifications += notified
        self.failed_notifications += failed

        return notified, failed

    def get_subscription(self, subscription_id: str) -> Optional[AgentSubscription]:
        """Get subscription by ID"""
        return self.subscriptions.get(subscription_id)

    def get_agent_subscriptions(self, agent_id: str) -> List[AgentSubscription]:
        """Get all subscriptions for an agent"""
        subscription_ids = self.agent_subscriptions.get(agent_id, set())
        return [self.subscriptions[sid] for sid in subscription_ids if sid in self.subscriptions]

    def get_statistics(self) -> Dict[str, Any]:
        """Get subscription manager statistics"""
        active_subscriptions = sum(1 for s in self.subscriptions.values() if s.active)

        return {
            "total_subscriptions": len(self.subscriptions),
            "active_subscriptions": active_subscriptions,
            "total_agents": len(self.agent_subscriptions),
            "total_notifications": self.total_notifications,
            "failed_notifications": self.failed_notifications,
            "success_rate": (
                (self.total_notifications / (self.total_notifications + self.failed_notifications))
                if (self.total_notifications + self.failed_notifications) > 0 else 1.0
            ),
            "subscriptions_by_signal": {
                signal.value: len(subs) for signal, subs in self.signal_subscriptions.items()
            },
            "subscriptions_by_project": {
                project: len(subs) for project, subs in self.project_subscriptions.items()
            }
        }

    async def cleanup_inactive(self) -> int:
        """Clean up inactive subscriptions"""
        if time.time() - self.last_cleanup < 300:  # Only cleanup every 5 minutes
            return 0

        inactive_ids = [
            sid for sid, sub in self.subscriptions.items()
            if not sub.active or (time.time() - sub.last_notification > 3600)  # 1 hour timeout
        ]

        count = 0
        for subscription_id in inactive_ids:
            if self.unsubscribe(subscription_id):
                count += 1

        self.last_cleanup = time.time()
        return count


class PersistenceManager:
    """Advanced persistence manager with multiple storage backends"""

    def __init__(
        self,
        storage_path: Path,
        mode: PersistenceMode = PersistenceMode.HYBRID,
        max_history_size: int = 10000,
        compression: bool = True
    ):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.mode = mode
        self.max_history_size = max_history_size
        self.compression = compression

        # File paths
        self.history_file = self.storage_path / "pheromone_history.jsonl"
        self.trails_file = self.storage_path / "pheromone_trails.json"
        self.db_file = self.storage_path / "pheromones.db"

        # In-memory caches
        self.recent_pheromones = deque(maxlen=max_history_size)
        self.trails_cache = {}
        self.pheromone_index = {}  # id -> pheromone mapping

        # Database connection (if using database mode)
        self.db_connection = None

        # Load existing data
        self._initialize_storage()
        self._load_existing_data()

    def _initialize_storage(self):
        """Initialize storage backend"""
        if self.mode in [PersistenceMode.DATABASE, PersistenceMode.HYBRID]:
            self._initialize_database()

    def _initialize_database(self):
        """Initialize SQLite database"""
        try:
            self.db_connection = sqlite3.connect(str(self.db_file), check_same_thread=False)
            cursor = self.db_connection.cursor()

            # Create pheromones table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pheromones (
                    id TEXT PRIMARY KEY,
                    signal TEXT NOT NULL,
                    payload TEXT NOT NULL,
                    timestamp REAL NOT NULL,
                    priority INTEGER NOT NULL,
                    agent_id TEXT,
                    project_id TEXT,
                    trail_id TEXT,
                    ttl REAL,
                    decay_rate REAL,
                    strength REAL,
                    tags TEXT,
                    target_agents TEXT,
                    source_agent TEXT,
                    persisted INTEGER,
                    version INTEGER,
                    created_at REAL DEFAULT (julianday('now'))
                )
            ''')

            # Create trails table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trails (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    project_id TEXT,
                    agent_id TEXT,
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL,
                    metadata TEXT,
                    max_pheromones INTEGER,
                    auto_cleanup INTEGER,
                    retention_hours INTEGER
                )
            ''')

            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pheromones_timestamp ON pheromones(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pheromones_signal ON pheromones(signal)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pheromones_project ON pheromones(project_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_pheromones_agent ON pheromones(agent_id)')

            self.db_connection.commit()
            logger.info("Database initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            self.db_connection = None

    def _load_existing_data(self):
        """Load existing data from storage"""
        if self.mode in [PersistenceMode.FILE_BASED, PersistenceMode.HYBRID]:
            self._load_file_history()
            self._load_file_trails()

        if self.mode in [PersistenceMode.DATABASE, PersistenceMode.HYBRID]:
            self._load_database_data()

    def _load_file_history(self):
        """Load pheromone history from file"""
        if not self.history_file.exists():
            return

        try:
            # Determine if file is compressed
            is_compressed = str(self.history_file).endswith('.gz')

            if is_compressed:
                open_func = gzip.open
                mode = 'rt'
                encoding = 'utf-8'
            else:
                open_func = open
                mode = 'r'
                encoding = 'utf-8'

            with open_func(self.history_file, mode, encoding=encoding) as f:
                for line in f:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            pheromone = Pheromone.from_dict(data)
                            self.recent_pheromones.append(pheromone)
                            self.pheromone_index[pheromone.id] = pheromone
                        except Exception as e:
                            logger.warning(f"Error loading pheromone from line: {e}")

            logger.info(f"Loaded {len(self.recent_pheromones)} pheromones from file")

        except Exception as e:
            logger.error(f"Error loading pheromone history: {e}")

    def _load_file_trails(self):
        """Load pheromone trails from file"""
        if not self.trails_file.exists():
            return

        try:
            with open(self.trails_file, 'r') as f:
                data = json.load(f)
                for trail_data in data.values():
                    trail = PheromoneTrail.from_dict(trail_data)
                    self.trails_cache[trail.id] = trail

            logger.info(f"Loaded {len(self.trails_cache)} trails from file")

        except Exception as e:
            logger.error(f"Error loading pheromone trails: {e}")

    def _load_database_data(self):
        """Load data from database"""
        if not self.db_connection:
            return

        try:
            cursor = self.db_connection.cursor()

            # Load recent pheromones
            cursor.execute('''
                SELECT * FROM pheromones
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (self.max_history_size,))

            for row in cursor.fetchall():
                pheromone = self._row_to_pheromone(row)
                if pheromone:
                    self.recent_pheromones.append(pheromone)
                    self.pheromone_index[pheromone.id] = pheromone

            # Load trails
            cursor.execute('SELECT * FROM trails')
            for row in cursor.fetchall():
                trail = self._row_to_trail(row)
                if trail:
                    self.trails_cache[trail.id] = trail

            logger.info(f"Loaded {len(self.recent_pheromones)} pheromones and {len(self.trails_cache)} trails from database")

        except Exception as e:
            logger.error(f"Error loading database data: {e}")

    def _row_to_pheromone(self, row) -> Optional[Pheromone]:
        """Convert database row to Pheromone object"""
        try:
            return Pheromone(
                id=row[0],
                signal=SignalType(row[1]),
                payload=json.loads(row[2]),
                timestamp=row[3],
                priority=SignalPriority(row[4]),
                agent_id=row[5],
                project_id=row[6],
                trail_id=row[7],
                ttl=row[8],
                decay_rate=row[9] or 0.0,
                strength=row[10] or 1.0,
                tags=set(json.loads(row[11] or '[]')),
                target_agents=set(json.loads(row[12] or '[]')),
                source_agent=row[13],
                persisted=bool(row[14]),
                version=row[15] or 1
            )
        except Exception as e:
            logger.error(f"Error converting row to pheromone: {e}")
            return None

    def _row_to_trail(self, row) -> Optional[PheromoneTrail]:
        """Convert database row to PheromoneTrail object"""
        try:
            return PheromoneTrail(
                id=row[0],
                name=row[1],
                description=row[2] or "",
                project_id=row[3],
                agent_id=row[4],
                created_at=row[5],
                updated_at=row[6],
                metadata=json.loads(row[7] or '{}'),
                max_pheromones=row[8] or 1000,
                auto_cleanup=bool(row[9]),
                retention_hours=row[10] or 24
            )
        except Exception as e:
            logger.error(f"Error converting row to trail: {e}")
            return None

    async def persist_pheromone(self, pheromone: Pheromone) -> bool:
        """Persist pheromone to storage"""
        try:
            # Add to in-memory cache
            self.recent_pheromones.append(pheromone)
            self.pheromone_index[pheromone.id] = pheromone

            # Persist based on mode
            if self.mode in [PersistenceMode.FILE_BASED, PersistenceMode.HYBRID]:
                await self._persist_to_file(pheromone)

            if self.mode in [PersistenceMode.DATABASE, PersistenceMode.HYBRID]:
                await self._persist_to_database(pheromone)

            pheromone.persisted = True
            return True

        except Exception as e:
            logger.error(f"Error persisting pheromone {pheromone.id}: {e}")
            return False

    async def _persist_to_file(self, pheromone: Pheromone) -> None:
        """Persist pheromone to file"""
        if self.compression:
            # For compressed files, use .gz extension
            if not str(self.history_file).endswith('.gz'):
                self.history_file = self.history_file.with_suffix('.jsonl.gz')
            open_func = gzip.open
            mode = 'at'
            encoding = 'utf-8'
        else:
            open_func = open
            mode = 'a'
            encoding = 'utf-8'

        with open_func(self.history_file, mode, encoding=encoding) as f:
            f.write(json.dumps(pheromone.to_dict()) + '\n')

    async def _persist_to_database(self, pheromone: Pheromone) -> None:
        """Persist pheromone to database"""
        if not self.db_connection:
            return

        cursor = self.db_connection.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO pheromones
            (id, signal, payload, timestamp, priority, agent_id, project_id, trail_id,
             ttl, decay_rate, strength, tags, target_agents, source_agent, persisted, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            pheromone.id,
            pheromone.signal.value,
            json.dumps(pheromone.payload),
            pheromone.timestamp,
            pheromone.priority.value,
            pheromone.agent_id,
            pheromone.project_id,
            pheromone.trail_id,
            pheromone.ttl,
            pheromone.decay_rate,
            pheromone.strength,
            json.dumps(list(pheromone.tags)),
            json.dumps(list(pheromone.target_agents)),
            pheromone.source_agent,
            1 if pheromone.persisted else 0,
            pheromone.version
        ))
        self.db_connection.commit()

    async def persist_trail(self, trail: PheromoneTrail) -> bool:
        """Persist pheromone trail to storage"""
        try:
            self.trails_cache[trail.id] = trail

            if self.mode in [PersistenceMode.FILE_BASED, PersistenceMode.HYBRID]:
                await self._persist_trails_to_file()

            if self.mode in [PersistenceMode.DATABASE, PersistenceMode.HYBRID]:
                await self._persist_trail_to_database(trail)

            return True

        except Exception as e:
            logger.error(f"Error persisting trail {trail.id}: {e}")
            return False

    async def _persist_trails_to_file(self) -> None:
        """Persist all trails to file"""
        trails_data = {
            trail_id: trail.to_dict()
            for trail_id, trail in self.trails_cache.items()
        }

        with open(self.trails_file, 'w') as f:
            json.dump(trails_data, f, indent=2)

    async def _persist_trail_to_database(self, trail: PheromoneTrail) -> None:
        """Persist trail to database"""
        if not self.db_connection:
            return

        cursor = self.db_connection.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO trails
            (id, name, description, project_id, agent_id, created_at, updated_at,
             metadata, max_pheromones, auto_cleanup, retention_hours)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trail.id,
            trail.name,
            trail.description,
            trail.project_id,
            trail.agent_id,
            trail.created_at,
            trail.updated_at,
            json.dumps(trail.metadata),
            trail.max_pheromones,
            1 if trail.auto_cleanup else 0,
            trail.retention_hours
        ))
        self.db_connection.commit()

    def get_pheromones(
        self,
        filter_criteria: Dict[str, Any] = None,
        limit: int = None,
        since: float = None,
        use_database: bool = None
    ) -> List[Pheromone]:
        """Retrieve pheromones with optional filtering"""

        # Determine source
        if use_database is None:
            use_database = self.mode in [PersistenceMode.DATABASE, PersistenceMode.HYBRID]

        if use_database and self.db_connection:
            return self._get_pheromones_from_database(filter_criteria, limit, since)
        else:
            return self._get_pheromones_from_memory(filter_criteria, limit, since)

    def _get_pheromones_from_memory(
        self,
        filter_criteria: Dict[str, Any] = None,
        limit: int = None,
        since: float = None
    ) -> List[Pheromone]:
        """Get pheromones from in-memory cache"""
        pheromones = list(self.recent_pheromones)

        # Apply time filter
        if since:
            pheromones = [p for p in pheromones if p.timestamp >= since]

        # Apply other filters
        if filter_criteria:
            pheromones = [p for p in pheromones if p.matches_filter(filter_criteria)]

        # Sort by timestamp (newest first)
        pheromones.sort(key=lambda p: p.timestamp, reverse=True)

        # Apply limit
        if limit:
            pheromones = pheromones[:limit]

        return pheromones

    def _get_pheromones_from_database(
        self,
        filter_criteria: Dict[str, Any] = None,
        limit: int = None,
        since: float = None
    ) -> List[Pheromone]:
        """Get pheromones from database"""
        if not self.db_connection:
            return []

        try:
            cursor = self.db_connection.cursor()

            # Build query
            query = "SELECT * FROM pheromones WHERE 1=1"
            params = []

            if since:
                query += " AND timestamp >= ?"
                params.append(since)

            if filter_criteria:
                if "signal" in filter_criteria:
                    query += " AND signal = ?"
                    params.append(filter_criteria["signal"].value if isinstance(filter_criteria["signal"], SignalType) else filter_criteria["signal"])

                if "project_id" in filter_criteria:
                    query += " AND project_id = ?"
                    params.append(filter_criteria["project_id"])

                if "agent_id" in filter_criteria:
                    query += " AND agent_id = ?"
                    params.append(filter_criteria["agent_id"])

            query += " ORDER BY timestamp DESC"

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            return [self._row_to_pheromone(row) for row in rows if self._row_to_pheromone(row)]

        except Exception as e:
            logger.error(f"Error querying database: {e}")
            return []

    def get_trail(self, trail_id: str) -> Optional[PheromoneTrail]:
        """Get pheromone trail by ID"""
        return self.trails_cache.get(trail_id)

    def get_trails(self, project_id: str = None) -> List[PheromoneTrail]:
        """Get all trails, optionally filtered by project"""
        trails = list(self.trails_cache.values())
        if project_id:
            trails = [t for t in trails if t.project_id == project_id]
        return trails

    async def cleanup_expired(self, max_age_hours: int = 24) -> int:
        """Clean up expired pheromones and return count removed"""
        cutoff_time = time.time() - (max_age_hours * 3600)

        # Remove from in-memory cache
        original_size = len(self.recent_pheromones)
        valid_pheromones = deque(maxlen=self.max_history_size)

        for pheromone in self.recent_pheromones:
            if pheromone.timestamp >= cutoff_time and not pheromone.is_expired():
                valid_pheromones.append(pheromone)
            else:
                self.pheromone_index.pop(pheromone.id, None)

        self.recent_pheromones = valid_pheromones
        removed_count = original_size - len(self.recent_pheromones)

        # Clean up database if using it
        if self.mode in [PersistenceMode.DATABASE, PersistenceMode.HYBRID] and self.db_connection:
            try:
                cursor = self.db_connection.cursor()
                cursor.execute('DELETE FROM pheromones WHERE timestamp < ?', (cutoff_time,))
                self.db_connection.commit()
            except Exception as e:
                logger.error(f"Error cleaning up database: {e}")

        # Clean up trails
        for trail in self.trails_cache.values():
            trail.cleanup_expired()

        return removed_count

    def close(self):
        """Close persistence manager and cleanup resources"""
        if self.db_connection:
            self.db_connection.close()
            self.db_connection = None
    
class PheromindBus:
    """
    Main Pheromind communication bus with comprehensive features

    This is the core class that orchestrates all pheromone-based communication
    with persistence, subscriptions, and advanced coordination features.
    """

    def __init__(
        self,
        storage_path: str = "pheromone_data",
        persistence_mode: PersistenceMode = PersistenceMode.HYBRID,
        max_history_size: int = 10000,
        enable_compression: bool = True,
        cleanup_interval_hours: int = 1
    ):
        # Core components
        self.storage_path = Path(storage_path)
        self.persistence = PersistenceManager(
            self.storage_path,
            persistence_mode,
            max_history_size,
            enable_compression
        )
        self.subscriptions = SubscriptionManager()

        # Trail management
        self.trails: Dict[str, PheromoneTrail] = {}

        # Performance and statistics
        self.stats = {
            "total_pheromones": 0,
            "total_trails": 0,
            "total_subscriptions": 0,
            "messages_per_minute": 0,
            "last_activity": None,
            "start_time": time.time()
        }

        # Background tasks
        self.cleanup_interval_hours = cleanup_interval_hours
        self._cleanup_task = None
        self._stats_task = None
        self._running = False

        # Load existing data
        self._load_existing_trails()

        logger.info(f"PheromindBus initialized with {persistence_mode.value} persistence")

    def _load_existing_trails(self):
        """Load existing trails from persistence"""
        try:
            trails = self.persistence.get_trails()
            for trail in trails:
                self.trails[trail.id] = trail

            self.stats["total_trails"] = len(self.trails)
            logger.info(f"Loaded {len(self.trails)} existing trails")

        except Exception as e:
            logger.error(f"Error loading existing trails: {e}")

    async def start(self):
        """Start the pheromone bus and background tasks"""
        if self._running:
            return

        self._running = True

        # Start background tasks
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._stats_task = asyncio.create_task(self._stats_loop())

        logger.info("PheromindBus started")

    async def stop(self):
        """Stop the pheromone bus and cleanup"""
        if not self._running:
            return

        self._running = False

        # Cancel background tasks
        if self._cleanup_task:
            self._cleanup_task.cancel()
        if self._stats_task:
            self._stats_task.cancel()

        # Flush any pending batched notifications
        for subscription in self.subscriptions.subscriptions.values():
            await subscription.flush_batch()

        # Final cleanup
        await self.persistence.cleanup_expired()
        self.persistence.close()

        logger.info("PheromindBus stopped")

    async def drop_pheromone(
        self,
        signal: Union[str, SignalType],
        payload: Dict[str, Any],
        project_id: str = None,
        agent_id: str = None,
        trail_id: str = None,
        priority: Union[int, SignalPriority] = SignalPriority.NORMAL,
        ttl: float = None,
        tags: Set[str] = None,
        target_agents: Set[str] = None
    ) -> str:
        """Drop a pheromone signal into the bus"""

        # Create pheromone
        pheromone = Pheromone.create(
            signal=signal,
            payload=payload,
            project_id=project_id,
            agent_id=agent_id,
            trail_id=trail_id,
            priority=priority,
            ttl=ttl,
            tags=tags,
            target_agents=target_agents
        )

        # Add to trail if specified
        if trail_id and trail_id in self.trails:
            self.trails[trail_id].add_pheromone(pheromone)

        # Persist pheromone
        await self.persistence.persist_pheromone(pheromone)

        # Notify subscribers
        notified, failed = await self.subscriptions.notify_subscribers(pheromone)

        # Update statistics
        self.stats["total_pheromones"] += 1
        self.stats["last_activity"] = time.time()

        logger.debug(
            f"Pheromone {pheromone.id} dropped: {signal} "
            f"(notified: {notified}, failed: {failed})"
        )

        return pheromone.id

    async def create_trail(
        self,
        name: str,
        description: str = "",
        project_id: str = None,
        agent_id: str = None,
        max_pheromones: int = 1000,
        retention_hours: int = 24
    ) -> str:
        """Create a new pheromone trail"""

        trail = PheromoneTrail(
            id=str(uuid.uuid4()),
            name=name,
            description=description,
            project_id=project_id,
            agent_id=agent_id,
            max_pheromones=max_pheromones,
            retention_hours=retention_hours
        )

        self.trails[trail.id] = trail
        await self.persistence.persist_trail(trail)

        self.stats["total_trails"] = len(self.trails)

        logger.info(f"Created trail {trail.id}: {name}")
        return trail.id

    def subscribe(
        self,
        agent_id: str,
        callback: Callable[[Pheromone], Any],
        filter_criteria: Dict[str, Any] = None,
        real_time: bool = True,
        batch_size: int = 1
    ) -> str:
        """Subscribe to pheromone signals"""

        subscription_id = self.subscriptions.subscribe(
            agent_id=agent_id,
            callback=callback,
            filter_criteria=filter_criteria,
            real_time=real_time,
            batch_size=batch_size
        )

        self.stats["total_subscriptions"] = len(self.subscriptions.subscriptions)

        return subscription_id

    def unsubscribe(self, subscription_id: str) -> bool:
        """Unsubscribe from pheromone signals"""
        success = self.subscriptions.unsubscribe(subscription_id)
        self.stats["total_subscriptions"] = len(self.subscriptions.subscriptions)
        return success

    def unsubscribe_agent(self, agent_id: str) -> int:
        """Unsubscribe all subscriptions for an agent"""
        count = self.subscriptions.unsubscribe_agent(agent_id)
        self.stats["total_subscriptions"] = len(self.subscriptions.subscriptions)
        return count

    def get_pheromones(
        self,
        filter_criteria: Dict[str, Any] = None,
        limit: int = None,
        since: float = None
    ) -> List[Pheromone]:
        """Get pheromones with filtering"""
        return self.persistence.get_pheromones(filter_criteria, limit, since)

    def get_trail(self, trail_id: str) -> Optional[PheromoneTrail]:
        """Get a specific trail"""
        return self.trails.get(trail_id)

    def get_trails(self, project_id: str = None) -> List[PheromoneTrail]:
        """Get trails, optionally filtered by project"""
        trails = list(self.trails.values())
        if project_id:
            trails = [t for t in trails if t.project_id == project_id]
        return trails

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive bus statistics"""
        persistence_stats = {
            "total_pheromones_persisted": len(self.persistence.recent_pheromones),
            "total_trails_persisted": len(self.persistence.trails_cache)
        }

        subscription_stats = self.subscriptions.get_statistics()

        # Calculate uptime
        uptime_seconds = time.time() - self.stats["start_time"]

        # Calculate messages per minute
        if uptime_seconds > 0:
            messages_per_minute = (self.stats["total_pheromones"] / uptime_seconds) * 60
        else:
            messages_per_minute = 0

        return {
            **self.stats,
            **persistence_stats,
            **subscription_stats,
            "uptime_seconds": uptime_seconds,
            "messages_per_minute": messages_per_minute,
            "storage_path": str(self.storage_path),
            "persistence_mode": self.persistence.mode.value,
            "running": self._running
        }

    async def _cleanup_loop(self):
        """Background cleanup task"""
        while self._running:
            try:
                await asyncio.sleep(self.cleanup_interval_hours * 3600)

                # Cleanup expired pheromones
                removed = await self.persistence.cleanup_expired()

                # Cleanup inactive subscriptions
                inactive_removed = await self.subscriptions.cleanup_inactive()

                # Cleanup trails
                for trail in self.trails.values():
                    trail.cleanup_expired()

                logger.info(
                    f"Cleanup completed: {removed} pheromones, "
                    f"{inactive_removed} subscriptions removed"
                )

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup task error: {e}")

    async def _stats_loop(self):
        """Background statistics update task"""
        while self._running:
            try:
                await asyncio.sleep(60)  # Update every minute

                # Update statistics
                self.stats["total_trails"] = len(self.trails)
                self.stats["total_subscriptions"] = len(self.subscriptions.subscriptions)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Stats task error: {e}")

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check and return status"""
        try:
            # Test persistence
            test_pheromone = Pheromone.create(
                signal=SignalType.HEALTH_CHECK,
                payload={"test": True, "timestamp": time.time()}
            )

            persist_success = await self.persistence.persist_pheromone(test_pheromone)

            # Test subscriptions
            subscription_count = len(self.subscriptions.subscriptions)

            return {
                "status": "healthy",
                "persistence_working": persist_success,
                "subscriptions_active": subscription_count,
                "trails_active": len(self.trails),
                "uptime_seconds": time.time() - self.stats["start_time"],
                "last_activity": self.stats["last_activity"],
                "running": self._running
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "running": self._running
            }


class PheromoneAnalytics:
    """Advanced analytics for pheromone patterns and trends"""

    def __init__(self, bus: PheromindBus):
        self.bus = bus

    def analyze_signal_patterns(
        self,
        project_id: str = None,
        time_window_hours: int = 24
    ) -> Dict[str, Any]:
        """Analyze signal patterns over time"""
        since = time.time() - (time_window_hours * 3600)

        filter_criteria = {"project_id": project_id} if project_id else {}
        pheromones = self.bus.get_pheromones(filter_criteria, since=since)

        if not pheromones:
            return {"error": "No pheromones found in time window"}

        # Signal frequency analysis
        signal_counts = defaultdict(int)
        agent_activity = defaultdict(int)
        hourly_activity = defaultdict(int)

        for pheromone in pheromones:
            signal_counts[pheromone.signal.value] += 1
            if pheromone.agent_id:
                agent_activity[pheromone.agent_id] += 1

            # Group by hour
            hour = int(pheromone.timestamp // 3600)
            hourly_activity[hour] += 1

        # Calculate trends
        total_signals = len(pheromones)
        avg_per_hour = total_signals / time_window_hours if time_window_hours > 0 else 0

        # Find most active periods
        peak_hour = max(hourly_activity.items(), key=lambda x: x[1]) if hourly_activity else (0, 0)

        return {
            "time_window_hours": time_window_hours,
            "total_signals": total_signals,
            "unique_signal_types": len(signal_counts),
            "unique_agents": len(agent_activity),
            "avg_signals_per_hour": avg_per_hour,
            "signal_distribution": dict(signal_counts),
            "agent_activity": dict(agent_activity),
            "peak_activity": {
                "hour": peak_hour[0],
                "count": peak_hour[1]
            },
            "hourly_distribution": dict(hourly_activity)
        }

    def detect_anomalies(
        self,
        project_id: str = None,
        threshold_multiplier: float = 2.0
    ) -> List[Dict[str, Any]]:
        """Detect anomalous pheromone patterns"""
        # Get recent activity
        recent_pheromones = self.bus.get_pheromones(
            {"project_id": project_id} if project_id else {},
            since=time.time() - 3600  # Last hour
        )

        # Get historical baseline (last 24 hours)
        historical_pheromones = self.bus.get_pheromones(
            {"project_id": project_id} if project_id else {},
            since=time.time() - (24 * 3600)
        )

        if len(historical_pheromones) < 10:  # Need minimum data
            return []

        # Calculate baseline metrics
        historical_rate = len(historical_pheromones) / 24  # per hour
        recent_rate = len(recent_pheromones)  # last hour

        anomalies = []

        # Rate anomaly detection
        if recent_rate > historical_rate * threshold_multiplier:
            anomalies.append({
                "type": "high_activity",
                "description": f"Activity rate {recent_rate:.1f}/hour is {recent_rate/historical_rate:.1f}x normal",
                "severity": "high" if recent_rate > historical_rate * 3 else "medium",
                "metrics": {
                    "recent_rate": recent_rate,
                    "baseline_rate": historical_rate,
                    "multiplier": recent_rate / historical_rate if historical_rate > 0 else float('inf')
                }
            })
        elif recent_rate < historical_rate / threshold_multiplier and historical_rate > 1:
            anomalies.append({
                "type": "low_activity",
                "description": f"Activity rate {recent_rate:.1f}/hour is unusually low",
                "severity": "medium",
                "metrics": {
                    "recent_rate": recent_rate,
                    "baseline_rate": historical_rate,
                    "multiplier": recent_rate / historical_rate if historical_rate > 0 else 0
                }
            })

        # Error signal detection
        error_signals = [p for p in recent_pheromones if p.signal == SignalType.ERROR]
        if len(error_signals) > len(recent_pheromones) * 0.1:  # More than 10% errors
            anomalies.append({
                "type": "high_error_rate",
                "description": f"High error rate: {len(error_signals)} errors in last hour",
                "severity": "high",
                "metrics": {
                    "error_count": len(error_signals),
                    "total_signals": len(recent_pheromones),
                    "error_rate": len(error_signals) / len(recent_pheromones) if recent_pheromones else 0
                }
            })

        return anomalies

    def generate_insights(self, project_id: str = None) -> Dict[str, Any]:
        """Generate insights about pheromone communication patterns"""
        patterns = self.analyze_signal_patterns(project_id)
        anomalies = self.detect_anomalies(project_id)

        insights = []

        # Activity insights
        if patterns.get("avg_signals_per_hour", 0) > 10:
            insights.append("High communication activity detected - system is very active")
        elif patterns.get("avg_signals_per_hour", 0) < 1:
            insights.append("Low communication activity - system may be idle or experiencing issues")

        # Agent insights
        agent_count = patterns.get("unique_agents", 0)
        if agent_count > 5:
            insights.append(f"High agent participation ({agent_count} agents) - good collaboration")
        elif agent_count == 1:
            insights.append("Single agent activity - may indicate isolation or specialized task")

        # Signal diversity insights
        signal_types = patterns.get("unique_signal_types", 0)
        if signal_types > 8:
            insights.append("High signal diversity - complex coordination patterns")
        elif signal_types < 3:
            insights.append("Low signal diversity - simple or focused activity")

        return {
            "patterns": patterns,
            "anomalies": anomalies,
            "insights": insights,
            "recommendations": self._generate_recommendations(patterns, anomalies)
        }

    def _generate_recommendations(
        self,
        patterns: Dict[str, Any],
        anomalies: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []

        # Based on anomalies
        for anomaly in anomalies:
            if anomaly["type"] == "high_error_rate":
                recommendations.append("Investigate error sources and implement error handling")
            elif anomaly["type"] == "high_activity":
                recommendations.append("Monitor system resources and consider scaling")
            elif anomaly["type"] == "low_activity":
                recommendations.append("Check agent health and connectivity")

        # Based on patterns
        avg_rate = patterns.get("avg_signals_per_hour", 0)
        if avg_rate > 50:
            recommendations.append("Consider implementing message batching to reduce overhead")

        agent_count = patterns.get("unique_agents", 0)
        if agent_count > 10:
            recommendations.append("Consider implementing agent groups for better organization")

        return recommendations

# Global pheromone bus instance
_global_bus: Optional[PheromindBus] = None

def get_pheromone_bus() -> PheromindBus:
    """Get the global pheromone bus instance"""
    global _global_bus
    if _global_bus is None:
        _global_bus = PheromindBus()
    return _global_bus

async def initialize_pheromone_bus(
    storage_path: str = "pheromone_data",
    persistence_mode: PersistenceMode = PersistenceMode.HYBRID
) -> PheromindBus:
    """Initialize and start the global pheromone bus"""
    global _global_bus
    if _global_bus is None:
        _global_bus = PheromindBus(storage_path, persistence_mode)

    await _global_bus.start()
    return _global_bus

async def shutdown_pheromone_bus():
    """Shutdown the global pheromone bus"""
    global _global_bus
    if _global_bus:
        await _global_bus.stop()
        _global_bus = None

# Convenience functions for backward compatibility
async def drop_pheromone(
    signal: Union[str, SignalType],
    payload: Dict[str, Any],
    project_id: str = None,
    agent_id: str = None,
    trail_id: str = None,
    priority: Union[int, SignalPriority] = SignalPriority.NORMAL
) -> str:
    """Convenience function to drop a pheromone"""
    bus = get_pheromone_bus()
    return await bus.drop_pheromone(
        signal=signal,
        payload=payload,
        project_id=project_id,
        agent_id=agent_id,
        trail_id=trail_id,
        priority=priority
    )

def get_pheromones(
    signal: Union[str, SignalType] = None,
    project_id: str = None,
    agent_id: str = None,
    limit: int = None,
    since: float = None
) -> List[Pheromone]:
    """Convenience function to get pheromones"""
    bus = get_pheromone_bus()

    filter_criteria = {}
    if signal:
        filter_criteria["signal"] = SignalType(signal) if isinstance(signal, str) else signal
    if project_id:
        filter_criteria["project_id"] = project_id
    if agent_id:
        filter_criteria["agent_id"] = agent_id

    return bus.get_pheromones(filter_criteria, limit, since)

def subscribe_to_signals(
    agent_id: str,
    callback: Callable[[Pheromone], Any],
    signal_types: List[Union[str, SignalType]] = None,
    project_id: str = None
) -> str:
    """Convenience function to subscribe to signals"""
    bus = get_pheromone_bus()

    filter_criteria = {}
    if signal_types:
        # For multiple signal types, we'll need to handle this in the callback
        # For now, just use the first one
        if signal_types:
            filter_criteria["signal"] = SignalType(signal_types[0]) if isinstance(signal_types[0], str) else signal_types[0]
    if project_id:
        filter_criteria["project_id"] = project_id

    return bus.subscribe(agent_id, callback, filter_criteria)
