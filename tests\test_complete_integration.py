"""
Complete Integration Tests for Aetherforge
Tests the full system integration with all components
"""

import pytest
import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from component_adapters import ComponentManager
from agent_executors import create_agent_executor
from pheromone_bus import EnhancedPheromonebus
from project_generator import ProjectGenerationPipeline
from config_manager import ConfigurationManager, AetherforgeConfig

class TestCompleteIntegration:
    """Test complete system integration"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def config_manager(self, temp_dir):
        """Create test configuration manager"""
        config_file = Path(temp_dir) / "test_config.json"
        return ConfigurationManager(str(config_file))
    
    @pytest.fixture
    def pheromone_bus(self, temp_dir):
        """Create test pheromone bus"""
        pheromone_file = Path(temp_dir) / "test_pheromones.json"
        return EnhancedPheromonebus(str(pheromone_file))
    
    @pytest.mark.asyncio
    async def test_component_manager_initialization(self):
        """Test component manager initialization"""
        async with ComponentManager() as cm:
            assert cm.archon is not None
            assert cm.pheromind is not None
            assert cm.bmad is not None
            assert cm.mcp is not None
    
    @pytest.mark.asyncio
    async def test_component_health_checks(self):
        """Test component health checks"""
        async with ComponentManager() as cm:
            health_results = await cm.health_check_all()
            
            assert "archon" in health_results
            assert "pheromind" in health_results
            assert "bmad" in health_results
            assert "mcp" in health_results
            
            # Each component should have a status
            for component, result in health_results.items():
                assert "status" in result
                assert result["status"] in ["healthy", "unhealthy", "unreachable", "error"]
    
    def test_agent_executor_creation(self):
        """Test agent executor creation"""
        roles = ["analyst", "architect", "developer", "qa"]
        
        for role in roles:
            executor = create_agent_executor(role)
            assert executor is not None
            assert executor.role == role
    
    @pytest.mark.asyncio
    async def test_pheromone_bus_operations(self, pheromone_bus):
        """Test pheromone bus operations"""
        # Start cleanup task
        await pheromone_bus.start_cleanup_task()
        
        # Drop pheromones
        project_id = "test_project_123"
        
        pheromone_id = await pheromone_bus.drop_pheromone(
            "test_pheromone",
            {"message": "test data"},
            project_id
        )
        
        assert pheromone_id is not None
        
        # Get statistics
        stats = pheromone_bus.get_statistics()
        assert stats["total_pheromones"] >= 1
        assert stats["active_projects"] >= 1
        
        # Get project trails
        trails = pheromone_bus.get_project_trails(project_id)
        assert len(trails) >= 1
        
        # Cleanup
        await pheromone_bus.shutdown()
    
    @pytest.mark.asyncio
    async def test_agent_execution_workflow(self, temp_dir):
        """Test complete agent execution workflow"""
        project_path = Path(temp_dir) / "test_project"
        project_path.mkdir()
        
        # Test analyst execution
        analyst = create_agent_executor("analyst")
        context = {
            "prompt": "Create a simple todo application",
            "project_path": str(project_path),
            "project_id": "test_project",
            "project_type": "fullstack"
        }
        
        with patch.object(analyst, 'call_openai', return_value="Mock analysis result"):
            result = await analyst.execute(context)
            
            assert result["success"] is True
            assert len(result["outputs"]) > 0
            
            # Check if files were created
            docs_dir = project_path / "docs"
            assert docs_dir.exists()
            assert (docs_dir / "requirements.md").exists()
    
    @pytest.mark.asyncio
    async def test_project_generation_pipeline(self, temp_dir, pheromone_bus):
        """Test complete project generation pipeline"""
        project_path = Path(temp_dir) / "generated_project"
        
        pipeline = ProjectGenerationPipeline()
        pipeline.pheromone_bus = pheromone_bus
        
        # Mock the agent executors to avoid actual OpenAI calls
        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            # Create mock executors
            mock_analyst = Mock()
            mock_analyst.execute = AsyncMock(return_value={
                "success": True,
                "outputs": ["docs/requirements.md"],
                "summary": "Requirements analysis completed"
            })
            
            mock_architect = Mock()
            mock_architect.execute = AsyncMock(return_value={
                "success": True,
                "outputs": ["docs/architecture.md"],
                "summary": "Architecture design completed"
            })
            
            mock_developer = Mock()
            mock_developer.execute = AsyncMock(return_value={
                "success": True,
                "outputs": ["package.json", "src/App.tsx"],
                "summary": "Development completed",
                "files_created": 10
            })
            
            mock_qa = Mock()
            mock_qa.execute = AsyncMock(return_value={
                "success": True,
                "outputs": ["docs/test_plan.md"],
                "summary": "QA completed"
            })
            
            # Configure mock to return appropriate executor
            def mock_executor_factory(role):
                if role == "analyst":
                    return mock_analyst
                elif role == "architect":
                    return mock_architect
                elif role == "developer":
                    return mock_developer
                elif role == "qa":
                    return mock_qa
                else:
                    raise ValueError(f"Unknown role: {role}")
            
            mock_create_agent.side_effect = mock_executor_factory
            
            # Run project generation
            result = await pipeline.generate_project(
                prompt="Create a todo application with user authentication",
                project_name="TodoApp",
                project_type="fullstack",
                project_path=str(project_path)
            )
            
            # Verify results
            assert result["success"] is True
            assert result["phases_completed"] == 6
            assert "results" in result
            
            # Verify project structure was created
            assert project_path.exists()
            assert (project_path / ".aetherforge.json").exists()
            assert (project_path / "README.md").exists()
            assert (project_path / "docs").exists()
    
    def test_configuration_manager(self, config_manager):
        """Test configuration manager functionality"""
        # Test default configuration
        assert config_manager.config.version == "1.0.0"
        assert len(config_manager.workflows) > 0
        assert len(config_manager.agents) > 0
        assert len(config_manager.tech_stacks) > 0
        
        # Test workflow retrieval
        workflow = config_manager.get_workflow("greenfield-fullstack")
        assert workflow is not None
        assert workflow.name == "Greenfield Full Stack"
        assert "requirements_analysis" in workflow.phases
        
        # Test agent retrieval
        agent = config_manager.get_agent("analyst")
        assert agent is not None
        assert agent.role == "analyst"
        assert agent.model == "gpt-4"
        
        # Test tech stack retrieval
        stack = config_manager.get_tech_stack_for_project_type("fullstack")
        assert stack is not None
        assert "fullstack" in stack.project_types
        
        # Test configuration update
        config_manager.update_config(debug=True, log_level="debug")
        assert config_manager.config.debug is True
        assert config_manager.config.log_level == "debug"
    
    @pytest.mark.asyncio
    async def test_component_integration_with_fallbacks(self):
        """Test component integration with fallback mechanisms"""
        async with ComponentManager() as cm:
            # Test Archon with fallback
            team_result = await cm.archon.generate_agent_team(
                "Create a web application",
                "fullstack"
            )
            assert "team_id" in team_result
            assert "agents" in team_result
            assert len(team_result["agents"]) > 0
            
            # Test MCP with fallback
            research_result = await cm.mcp.research_technologies(
                "fullstack",
                ["web application", "user authentication"]
            )
            assert "project_type" in research_result
            assert "recommended_stack" in research_result
            
            # Test Pheromind with fallback
            bus_result = await cm.pheromind.initialize_pheromone_bus(
                team_result,
                "/test/path",
                "test_project"
            )
            assert "bus_id" in bus_result
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, temp_dir):
        """Test error handling and recovery mechanisms"""
        project_path = Path(temp_dir) / "error_test_project"
        
        # Test with invalid project path
        pipeline = ProjectGenerationPipeline()
        
        with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
            # Create a mock that raises an exception
            mock_executor = Mock()
            mock_executor.execute = AsyncMock(side_effect=Exception("Test error"))
            mock_create_agent.return_value = mock_executor
            
            result = await pipeline.generate_project(
                prompt="Test project",
                project_name="ErrorTest",
                project_type="fullstack",
                project_path=str(project_path)
            )
            
            # Should handle error gracefully
            assert result["success"] is False
            assert "error" in result
    
    def test_environment_configuration_loading(self, config_manager):
        """Test environment configuration loading"""
        env_config = config_manager.get_environment_config()
        
        # Should contain expected environment variables
        expected_vars = [
            "ORCHESTRATOR_URL",
            "PROJECTS_DIR",
            "LOG_LEVEL",
            "AETHERFORGE_ENV"
        ]
        
        for var in expected_vars:
            assert var in env_config
        
        # Test loading into environment
        from config_manager import load_environment_from_config
        
        original_env = os.environ.copy()
        try:
            # Clear some environment variables
            for var in expected_vars:
                if var in os.environ:
                    del os.environ[var]
            
            # Load from config
            load_environment_from_config()
            
            # Verify variables are set
            for var in expected_vars:
                assert var in os.environ
                
        finally:
            # Restore original environment
            os.environ.clear()
            os.environ.update(original_env)
    
    @pytest.mark.asyncio
    async def test_full_system_simulation(self, temp_dir, config_manager, pheromone_bus):
        """Test full system simulation end-to-end"""
        # This test simulates a complete project creation workflow
        project_path = Path(temp_dir) / "full_system_test"
        
        # Initialize all components
        async with ComponentManager() as cm:
            # Start pheromone bus
            await pheromone_bus.start_cleanup_task()
            
            # Create project generation pipeline
            pipeline = ProjectGenerationPipeline()
            pipeline.pheromone_bus = pheromone_bus
            
            # Mock all external dependencies
            with patch('src.agent_executors.create_agent_executor') as mock_create_agent:
                # Setup comprehensive mocks
                mock_executors = {}
                for role in ["analyst", "architect", "developer", "qa"]:
                    mock_executor = Mock()
                    mock_executor.execute = AsyncMock(return_value={
                        "success": True,
                        "outputs": [f"docs/{role}_output.md"],
                        "summary": f"{role.title()} phase completed successfully"
                    })
                    mock_executors[role] = mock_executor
                
                mock_create_agent.side_effect = lambda role: mock_executors[role]
                
                # Run complete workflow
                result = await pipeline.generate_project(
                    prompt="Create a comprehensive e-commerce platform with user authentication, product catalog, shopping cart, and payment integration",
                    project_name="EcommercePlatform",
                    project_type="fullstack",
                    project_path=str(project_path),
                    workflow="greenfield-fullstack"
                )
                
                # Verify complete success
                assert result["success"] is True
                assert result["phases_completed"] == 6
                
                # Verify project structure
                assert project_path.exists()
                assert (project_path / ".aetherforge.json").exists()
                
                # Verify metadata
                metadata_file = project_path / ".aetherforge.json"
                metadata = json.loads(metadata_file.read_text())
                assert metadata["status"] == "completed"
                assert metadata["name"] == "EcommercePlatform"
                
                # Verify pheromone activity
                stats = pheromone_bus.get_statistics()
                assert stats["total_pheromones"] > 0
                
                # Cleanup
                await pheromone_bus.shutdown()

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
