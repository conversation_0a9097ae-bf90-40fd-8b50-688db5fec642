"""
Real-time Pheromone Communication System for Aetherforge
Handles agent coordination, progress tracking, and real-time updates
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from pathlib import Path
import uuid
from collections import defaultdict, deque
import threading
import time

logger = logging.getLogger(__name__)

class PheromoneMessage:
    """Individual pheromone message with metadata"""
    
    def __init__(self, message_type: str, data: Dict[str, Any], 
                 project_id: str, agent_id: str = None, trail_id: str = None):
        self.id = str(uuid.uuid4())
        self.type = message_type
        self.data = data
        self.project_id = project_id
        self.agent_id = agent_id
        self.trail_id = trail_id
        self.timestamp = datetime.now()
        self.strength = 1.0
        self.processed = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "type": self.type,
            "data": self.data,
            "project_id": self.project_id,
            "agent_id": self.agent_id,
            "trail_id": self.trail_id,
            "timestamp": self.timestamp.isoformat(),
            "strength": self.strength,
            "processed": self.processed
        }

class PheromoneSubscriber:
    """Subscriber for pheromone events with filtering"""
    
    def __init__(self, subscriber_id: str, callback: Callable, filters: Dict[str, Any] = None):
        self.subscriber_id = subscriber_id
        self.callback = callback
        self.filters = filters or {}
        self.active = True
        self.message_count = 0
        self.last_activity = datetime.now()
    
    def matches_filter(self, message: PheromoneMessage) -> bool:
        """Check if message matches subscriber filters"""
        if not self.filters:
            return True
        
        for key, value in self.filters.items():
            if key == "project_id" and message.project_id != value:
                return False
            elif key == "agent_id" and message.agent_id != value:
                return False
            elif key == "type" and message.type != value:
                return False
            elif key == "trail_id" and message.trail_id != value:
                return False
        
        return True
    
    async def notify(self, message: PheromoneMessage):
        """Notify subscriber of new message"""
        if self.active and self.matches_filter(message):
            try:
                if asyncio.iscoroutinefunction(self.callback):
                    await self.callback(message.to_dict())
                else:
                    self.callback(message.to_dict())
                
                self.message_count += 1
                self.last_activity = datetime.now()
                
            except Exception as e:
                logger.warning(f"Subscriber {self.subscriber_id} callback failed: {e}")

class RealTimePheromoneSystem:
    """Real-time pheromone communication system"""
    
    def __init__(self, persistence_file: str = "pheromones_realtime.json"):
        self.messages: deque = deque(maxlen=10000)  # Keep last 10k messages
        self.subscribers: Dict[str, PheromoneSubscriber] = {}
        self.project_trails: Dict[str, List[PheromoneMessage]] = defaultdict(list)
        self.persistence_file = Path(persistence_file)
        
        # Statistics
        self.stats = {
            "total_messages": 0,
            "active_projects": 0,
            "active_subscribers": 0,
            "messages_per_second": 0,
            "last_activity": None,
            "system_start_time": datetime.now().isoformat()
        }
        
        # Background tasks
        self._cleanup_task = None
        self._stats_task = None
        self._persistence_task = None
        self._running = False
        
        # Message rate tracking
        self._message_timestamps = deque(maxlen=100)
        
        self._load_persisted_data()
    
    def _load_persisted_data(self):
        """Load persisted pheromone data"""
        try:
            if self.persistence_file.exists():
                with open(self.persistence_file, 'r') as f:
                    data = json.load(f)
                    
                    # Restore recent messages
                    for msg_data in data.get("recent_messages", []):
                        message = PheromoneMessage(
                            msg_data["type"], msg_data["data"], msg_data["project_id"],
                            msg_data.get("agent_id"), msg_data.get("trail_id")
                        )
                        message.timestamp = datetime.fromisoformat(msg_data["timestamp"])
                        message.strength = msg_data.get("strength", 1.0)
                        message.processed = msg_data.get("processed", False)
                        
                        self.messages.append(message)
                        self.project_trails[message.project_id].append(message)
                    
                    # Restore stats
                    if "stats" in data:
                        self.stats.update(data["stats"])
                        
                logger.info("Pheromone system data loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load pheromone data: {e}")
    
    def _persist_data(self):
        """Persist pheromone data to file"""
        try:
            # Keep only recent messages for persistence
            recent_messages = list(self.messages)[-1000:]  # Last 1000 messages
            
            data = {
                "recent_messages": [msg.to_dict() for msg in recent_messages],
                "stats": self.stats,
                "last_saved": datetime.now().isoformat()
            }
            
            with open(self.persistence_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to persist pheromone data: {e}")
    
    async def start(self):
        """Start the pheromone system background tasks"""
        if self._running:
            return
        
        self._running = True
        
        # Start background tasks
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._stats_task = asyncio.create_task(self._stats_loop())
        self._persistence_task = asyncio.create_task(self._persistence_loop())
        
        logger.info("Pheromone system started")
    
    async def stop(self):
        """Stop the pheromone system"""
        self._running = False
        
        # Cancel background tasks
        for task in [self._cleanup_task, self._stats_task, self._persistence_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Final persistence
        self._persist_data()
        
        # Deactivate all subscribers
        for subscriber in self.subscribers.values():
            subscriber.active = False
        
        logger.info("Pheromone system stopped")
    
    async def drop_pheromone(self, message_type: str, data: Dict[str, Any], 
                           project_id: str, agent_id: str = None, trail_id: str = None) -> str:
        """Drop a pheromone message and notify subscribers"""
        
        # Create message
        message = PheromoneMessage(message_type, data, project_id, agent_id, trail_id)
        
        # Add to collections
        self.messages.append(message)
        self.project_trails[project_id].append(message)
        
        # Track message rate
        self._message_timestamps.append(datetime.now())
        
        # Update statistics
        self.stats["total_messages"] += 1
        self.stats["active_projects"] = len(self.project_trails)
        self.stats["active_subscribers"] = len([s for s in self.subscribers.values() if s.active])
        self.stats["last_activity"] = datetime.now().isoformat()
        
        # Notify subscribers
        await self._notify_subscribers(message)
        
        logger.debug(f"Pheromone dropped: {message_type} for project {project_id}")
        return message.id
    
    async def _notify_subscribers(self, message: PheromoneMessage):
        """Notify all matching subscribers"""
        notification_tasks = []
        
        for subscriber in self.subscribers.values():
            if subscriber.active:
                notification_tasks.append(subscriber.notify(message))
        
        if notification_tasks:
            await asyncio.gather(*notification_tasks, return_exceptions=True)
    
    def subscribe(self, subscriber_id: str, callback: Callable, filters: Dict[str, Any] = None) -> str:
        """Subscribe to pheromone events"""
        subscriber = PheromoneSubscriber(subscriber_id, callback, filters)
        self.subscribers[subscriber_id] = subscriber
        
        logger.info(f"New subscriber: {subscriber_id} with filters: {filters}")
        return subscriber_id
    
    def unsubscribe(self, subscriber_id: str):
        """Unsubscribe from pheromone events"""
        if subscriber_id in self.subscribers:
            self.subscribers[subscriber_id].active = False
            del self.subscribers[subscriber_id]
            logger.info(f"Subscriber removed: {subscriber_id}")
    
    def get_project_messages(self, project_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent messages for a project"""
        project_messages = self.project_trails.get(project_id, [])
        recent_messages = project_messages[-limit:] if project_messages else []
        return [msg.to_dict() for msg in recent_messages]
    
    def get_messages_by_type(self, message_type: str, project_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get messages by type, optionally filtered by project"""
        filtered_messages = []
        
        for message in reversed(self.messages):
            if message.type == message_type:
                if project_id is None or message.project_id == project_id:
                    filtered_messages.append(message.to_dict())
                    if len(filtered_messages) >= limit:
                        break
        
        return filtered_messages
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        # Calculate messages per second
        now = datetime.now()
        recent_timestamps = [ts for ts in self._message_timestamps if (now - ts).seconds < 60]
        messages_per_minute = len(recent_timestamps)
        
        # Active projects
        active_projects = len(self.project_trails)
        
        # Subscriber stats
        active_subscribers = len([s for s in self.subscribers.values() if s.active])
        
        return {
            **self.stats,
            "messages_per_minute": messages_per_minute,
            "active_projects": active_projects,
            "active_subscribers": active_subscribers,
            "total_subscribers": len(self.subscribers),
            "recent_messages": len(self.messages),
            "projects_with_activity": list(self.project_trails.keys()),
            "uptime_seconds": (datetime.now() - datetime.fromisoformat(self.stats["system_start_time"])).total_seconds()
        }
    
    async def _cleanup_loop(self):
        """Background cleanup of old messages and inactive subscribers"""
        while self._running:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                # Clean up old project trails
                cutoff_time = datetime.now() - timedelta(hours=24)
                
                for project_id in list(self.project_trails.keys()):
                    messages = self.project_trails[project_id]
                    # Keep only recent messages
                    recent_messages = [msg for msg in messages if msg.timestamp > cutoff_time]
                    
                    if recent_messages:
                        self.project_trails[project_id] = recent_messages
                    else:
                        del self.project_trails[project_id]
                
                # Clean up inactive subscribers
                inactive_subscribers = [
                    sid for sid, sub in self.subscribers.items()
                    if not sub.active or (datetime.now() - sub.last_activity).hours > 1
                ]
                
                for sid in inactive_subscribers:
                    self.unsubscribe(sid)
                
                logger.debug("Pheromone cleanup completed")
                
            except Exception as e:
                logger.error(f"Cleanup task error: {e}")
    
    async def _stats_loop(self):
        """Background statistics calculation"""
        while self._running:
            try:
                await asyncio.sleep(60)  # Update every minute
                
                # Update statistics
                self.stats["active_projects"] = len(self.project_trails)
                self.stats["active_subscribers"] = len([s for s in self.subscribers.values() if s.active])
                
            except Exception as e:
                logger.error(f"Stats task error: {e}")
    
    async def _persistence_loop(self):
        """Background data persistence"""
        while self._running:
            try:
                await asyncio.sleep(600)  # Persist every 10 minutes
                self._persist_data()
                
            except Exception as e:
                logger.error(f"Persistence task error: {e}")

# Global pheromone system instance
_global_pheromone_system = None

def get_pheromone_system() -> RealTimePheromoneSystem:
    """Get the global pheromone system instance"""
    global _global_pheromone_system
    if _global_pheromone_system is None:
        _global_pheromone_system = RealTimePheromoneSystem()
    return _global_pheromone_system

async def drop_pheromone_realtime(message_type: str, data: Dict[str, Any], 
                                project_id: str, agent_id: str = None, trail_id: str = None) -> str:
    """Convenience function to drop a pheromone message"""
    system = get_pheromone_system()
    return await system.drop_pheromone(message_type, data, project_id, agent_id, trail_id)
