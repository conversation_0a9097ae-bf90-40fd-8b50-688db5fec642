{"name": "aetherforge", "displayName": "Aetherforge", "description": "Autonomous AI Software Creation System", "version": "1.0.0", "publisher": "aetherforge", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "aetherforge.start", "title": "Start Aetherforge", "category": "Aetherforge"}], "configuration": {"title": "Aetherforge", "properties": {"aetherforge.orchestratorUrl": {"type": "string", "default": "http://localhost:8000", "description": "URL of the Aetherforge orchestrator service"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "esbuild": "^0.19.12", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0"}}