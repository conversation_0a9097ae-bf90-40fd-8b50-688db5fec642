#!/usr/bin/env python3
"""
Complete Test Runner for Aetherforge
Runs all tests and validates the complete system integration
"""

import os
import sys
import subprocess
import json
import asyncio
import time
from pathlib import Path
from datetime import datetime
import requests

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def print_status(message, status="INFO"):
    """Print a status message"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    status_symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅",
        "ERROR": "❌",
        "WARNING": "⚠️",
        "RUNNING": "🔄"
    }
    symbol = status_symbols.get(status, "•")
    print(f"[{timestamp}] {symbol} {message}")

def check_dependencies():
    """Check if required dependencies are installed"""
    print_header("Checking Dependencies")
    
    required_packages = [
        "pytest",
        "pytest-asyncio",
        "fastapi",
        "uvicorn",
        "requests",
        "openai",
        "aiohttp"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print_status(f"{package} - installed", "SUCCESS")
        except ImportError:
            print_status(f"{package} - missing", "ERROR")
            missing_packages.append(package)
    
    if missing_packages:
        print_status(f"Missing packages: {', '.join(missing_packages)}", "ERROR")
        print_status("Install with: pip install " + " ".join(missing_packages), "INFO")
        return False
    
    print_status("All dependencies satisfied", "SUCCESS")
    return True

def check_environment():
    """Check environment configuration"""
    print_header("Checking Environment")
    
    required_env_vars = [
        "OPENAI_API_KEY"
    ]
    
    optional_env_vars = [
        "ANTHROPIC_API_KEY",
        "ORCHESTRATOR_URL",
        "PROJECTS_DIR"
    ]
    
    env_ok = True
    
    for var in required_env_vars:
        if var in os.environ and os.environ[var]:
            print_status(f"{var} - configured", "SUCCESS")
        else:
            print_status(f"{var} - missing (required)", "ERROR")
            env_ok = False
    
    for var in optional_env_vars:
        if var in os.environ and os.environ[var]:
            print_status(f"{var} - configured", "SUCCESS")
        else:
            print_status(f"{var} - using default", "WARNING")
    
    return env_ok

def run_unit_tests():
    """Run unit tests"""
    print_header("Running Unit Tests")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            "tests/", 
            "-v", 
            "--tb=short",
            "--disable-warnings"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print_status("Unit tests passed", "SUCCESS")
            return True
        else:
            print_status("Unit tests failed", "ERROR")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print_status("Unit tests timed out", "ERROR")
        return False
    except Exception as e:
        print_status(f"Error running unit tests: {e}", "ERROR")
        return False

def test_orchestrator_startup():
    """Test orchestrator startup"""
    print_header("Testing Orchestrator Startup")
    
    try:
        # Start orchestrator in background
        print_status("Starting orchestrator...", "RUNNING")
        
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "src.orchestrator:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for startup
        time.sleep(5)
        
        # Test health endpoint
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print_status(f"Orchestrator healthy: {health_data.get('status')}", "SUCCESS")
                
                # Test API documentation
                docs_response = requests.get("http://localhost:8000/docs", timeout=5)
                if docs_response.status_code == 200:
                    print_status("API documentation accessible", "SUCCESS")
                else:
                    print_status("API documentation not accessible", "WARNING")
                
                # Terminate process
                process.terminate()
                process.wait(timeout=5)
                
                return True
            else:
                print_status(f"Health check failed: {response.status_code}", "ERROR")
                
        except requests.exceptions.RequestException as e:
            print_status(f"Failed to connect to orchestrator: {e}", "ERROR")
        
        # Cleanup
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        return False
        
    except Exception as e:
        print_status(f"Error testing orchestrator: {e}", "ERROR")
        return False

def test_project_creation():
    """Test project creation end-to-end"""
    print_header("Testing Project Creation")
    
    try:
        # Start orchestrator
        print_status("Starting orchestrator for project test...", "RUNNING")
        
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "src.orchestrator:app", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for startup
        time.sleep(5)
        
        # Test project creation
        project_data = {
            "prompt": "Create a simple hello world web application for testing",
            "project_name": "TestProject",
            "project_type": "frontend"
        }
        
        print_status("Creating test project...", "RUNNING")
        
        response = requests.post(
            "http://localhost:8000/projects",
            json=project_data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            project_id = result.get("project_id")
            print_status(f"Project creation started: {project_id}", "SUCCESS")
            
            # Wait a bit for processing
            time.sleep(10)
            
            # Check project status
            projects_response = requests.get("http://localhost:8000/projects", timeout=10)
            if projects_response.status_code == 200:
                projects = projects_response.json()
                print_status(f"Found {len(projects)} projects", "INFO")
                
                # Check if our project exists
                test_project = next((p for p in projects if p.get("project_id") == project_id), None)
                if test_project:
                    print_status(f"Test project status: {test_project.get('status', 'unknown')}", "SUCCESS")
                else:
                    print_status("Test project not found in project list", "WARNING")
            
            # Cleanup
            process.terminate()
            process.wait(timeout=5)
            
            return True
        else:
            print_status(f"Project creation failed: {response.status_code}", "ERROR")
            print_status(f"Response: {response.text}", "ERROR")
        
        # Cleanup
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        return False
        
    except Exception as e:
        print_status(f"Error testing project creation: {e}", "ERROR")
        return False

def test_component_integration():
    """Test component integration"""
    print_header("Testing Component Integration")
    
    try:
        # Test component adapters
        sys.path.insert(0, 'src')
        
        from component_adapters import ComponentManager
        
        async def test_components():
            async with ComponentManager() as cm:
                print_status("Component manager initialized", "SUCCESS")
                
                # Test health checks
                health_results = await cm.health_check_all()
                
                for component, result in health_results.items():
                    status = result.get("status", "unknown")
                    if status == "healthy":
                        print_status(f"{component} - healthy", "SUCCESS")
                    elif status == "unreachable":
                        print_status(f"{component} - unreachable (expected)", "WARNING")
                    else:
                        print_status(f"{component} - {status}", "WARNING")
                
                return True
        
        # Run async test
        result = asyncio.run(test_components())
        return result
        
    except Exception as e:
        print_status(f"Error testing component integration: {e}", "ERROR")
        return False

def generate_test_report(results):
    """Generate a comprehensive test report"""
    print_header("Test Report")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print_status(f"Total Tests: {total_tests}", "INFO")
    print_status(f"Passed: {passed_tests}", "SUCCESS" if passed_tests == total_tests else "INFO")
    print_status(f"Failed: {failed_tests}", "ERROR" if failed_tests > 0 else "INFO")
    
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        symbol = "✅" if result else "❌"
        print(f"  {symbol} {test_name}: {status}")
    
    # Generate JSON report
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        },
        "results": results
    }
    
    report_file = Path("test_report.json")
    report_file.write_text(json.dumps(report, indent=2))
    print_status(f"Test report saved to {report_file}", "INFO")
    
    return passed_tests == total_tests

def main():
    """Main test runner"""
    print_header("Aetherforge Complete System Test")
    print_status("Starting comprehensive system validation", "INFO")
    
    # Track all test results
    results = {}
    
    # Run all tests
    results["dependencies"] = check_dependencies()
    results["environment"] = check_environment()
    
    if results["dependencies"] and results["environment"]:
        results["unit_tests"] = run_unit_tests()
        results["orchestrator_startup"] = test_orchestrator_startup()
        results["component_integration"] = test_component_integration()
        results["project_creation"] = test_project_creation()
    else:
        print_status("Skipping integration tests due to dependency/environment issues", "WARNING")
        results["unit_tests"] = False
        results["orchestrator_startup"] = False
        results["component_integration"] = False
        results["project_creation"] = False
    
    # Generate report
    success = generate_test_report(results)
    
    if success:
        print_header("🎉 All Tests Passed!")
        print_status("Aetherforge system is fully operational", "SUCCESS")
        return 0
    else:
        print_header("❌ Some Tests Failed")
        print_status("Please review the test report and fix issues", "ERROR")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
