FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies for testing
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install test dependencies
RUN pip install --no-cache-dir \
    pytest>=7.0.0 \
    pytest-asyncio>=0.21.0 \
    pytest-cov>=4.0.0 \
    pytest-html>=3.1.0 \
    pytest-xdist>=3.0.0 \
    requests>=2.28.0 \
    httpx>=0.24.0 \
    flake8>=6.0.0 \
    mypy>=1.0.0

# Copy source code and tests
COPY src/ ./src/
COPY tests/ ./tests/
COPY agent_executors.py .
COPY pheromone_bus_simple.py .
COPY run_tests.py .

# Set environment variables for testing
ENV PYTHONPATH=/app
ENV AETHERFORGE_ENV=test
ENV PROJECTS_DIR=/app/test_projects
ENV PHEROMONE_FILE=/app/test_pheromones.json

# Create test directories
RUN mkdir -p /app/test_projects /app/test_data

# Run tests by default
CMD ["python", "run_tests.py", "--all", "--verbose"]
