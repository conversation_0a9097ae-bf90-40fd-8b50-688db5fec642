#!/usr/bin/env python3
"""
Complete Enhanced Orchestrator Test
Tests all enhanced features of the orchestrator including new supporting functions
"""

import os
import sys
import asyncio
import tempfile
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_enhanced_project_creation():
    """Test the complete enhanced project creation flow"""
    print("🔮 Testing Enhanced Project Creation Flow...")
    
    try:
        from orchestrator import (
            ProjectRequest, ProjectType, AgentBehavior, Priority,
            validate_project_request, generate_project_metadata,
            create_project_structure, setup_project_logging,
            generate_enhanced_agent_team, initialize_enhanced_pheromone_bus,
            prepare_workflow_configuration, drop_enhanced_pheromone
        )
        
        print("   ✅ All enhanced functions imported successfully")
        
        # Create comprehensive test request
        test_request = ProjectRequest(
            prompt="Create a comprehensive e-commerce platform with user authentication, product catalog, shopping cart, payment processing, order management, and admin dashboard. Include real-time notifications, search functionality, and mobile responsiveness.",
            project_name="EcommerceHub",
            project_type=ProjectType.FULLSTACK,
            agent_behavior=AgentBehavior.PRODUCTION,
            priority=Priority.HIGH,
            enable_parallel_execution=True,
            max_iterations=5,
            enable_code_review=True,
            enable_testing=True,
            code_quality_level="production",
            documentation_level="comprehensive",
            test_coverage_target=0.9,
            enable_ai_optimization=True,
            enable_security_scan=True,
            enable_performance_optimization=True,
            target_platforms=["web", "mobile"],
            programming_languages=["Python", "JavaScript", "TypeScript"],
            frameworks=["FastAPI", "React", "React Native"],
            databases=["PostgreSQL", "Redis"],
            deployment_targets=["AWS", "Docker"],
            custom_templates={"api": "fastapi_template", "frontend": "react_template"},
            environment_variables={"NODE_ENV": "production", "DEBUG": "false"}
        )
        
        print("   ✅ Comprehensive test request created")
        print(f"      Project type: {test_request.project_type.value}")
        print(f"      Agent behavior: {test_request.agent_behavior.value}")
        print(f"      Priority: {test_request.priority.value}")
        print(f"      Languages: {', '.join(test_request.programming_languages)}")
        print(f"      Frameworks: {', '.join(test_request.frameworks)}")
        
        # Test validation
        await validate_project_request(test_request)
        print("   ✅ Request validation passed")
        
        # Test metadata generation
        project_id = "test_enhanced_123"
        metadata = await generate_project_metadata(test_request, project_id)
        print(f"   ✅ Project metadata generated")
        print(f"      Project slug: {metadata['slug']}")
        print(f"      Configuration keys: {len(metadata['configuration'])}")
        print(f"      Technical specs: {len(metadata['technical_specs'])}")
        
        # Test project structure creation
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / metadata['slug']
            
            await create_project_structure(project_path, metadata)
            print(f"   ✅ Project structure created")
            
            # Check created files
            created_files = list(project_path.rglob("*"))
            print(f"      Files/directories created: {len(created_files)}")
            
            # Check specific files
            expected_files = [".aetherforge.json", ".aetherforge_status.json", "README.md", ".gitignore"]
            for file in expected_files:
                if (project_path / file).exists():
                    print(f"      ✅ {file} created")
                else:
                    print(f"      ❌ {file} missing")
            
            # Test project logging setup
            project_logger = setup_project_logging(project_id, project_path)
            print(f"   ✅ Project logging setup: {project_logger.name}")
            
            # Test enhanced agent team generation
            agent_team = await generate_enhanced_agent_team(
                test_request, "fullstack_workflow", project_id, project_logger
            )
            print(f"   ✅ Enhanced agent team generated")
            print(f"      Team ID: {agent_team['team_id']}")
            print(f"      Agents: {len(agent_team['agents'])}")
            print(f"      Behavior: {agent_team['behavior']}")
            
            # Check for specialized agents
            roles = [agent['role'] for agent in agent_team['agents']]
            print(f"      Agent roles: {', '.join(roles)}")
            
            # Test enhanced pheromone bus initialization
            pheromone_bus = await initialize_enhanced_pheromone_bus(
                agent_team, str(project_path), project_id, test_request
            )
            print(f"   ✅ Enhanced pheromone bus initialized")
            print(f"      Bus ID: {pheromone_bus['bus_id']}")
            print(f"      Channels: {len(pheromone_bus.get('channels', {}))}")
            
            # Test workflow configuration
            workflow_config = await prepare_workflow_configuration(test_request, metadata)
            print(f"   ✅ Workflow configuration prepared")
            print(f"      Quality settings: {len(workflow_config['quality_settings'])}")
            print(f"      Technical constraints: {len(workflow_config['technical_constraints'])}")
            
            # Test enhanced pheromone dropping
            pheromone_id = await drop_enhanced_pheromone("test_enhanced_flow", {
                "test": "data",
                "project_metadata": metadata,
                "agent_team": agent_team,
                "workflow_config": workflow_config
            }, project_id=project_id)
            print(f"   ✅ Enhanced pheromone dropped: {pheromone_id}")
            
            # Cleanup logger to prevent file locking
            for handler in project_logger.handlers[:]:
                handler.close()
                project_logger.removeHandler(handler)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Enhanced project creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_api_endpoints():
    """Test enhanced API endpoints functionality"""
    print("🌐 Testing Enhanced API Endpoints...")
    
    try:
        from orchestrator import app
        
        # Check FastAPI app
        print(f"   ✅ FastAPI app: {app.title} v{app.version}")
        
        # Get all routes
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                for method in route.methods:
                    if method != 'HEAD':  # Skip HEAD methods
                        routes.append(f"{method} {route.path}")
        
        print(f"   ✅ API routes available: {len(routes)}")
        
        # Check for enhanced endpoints
        enhanced_endpoints = [
            "POST /projects",
            "GET /projects",
            "GET /health",
            "GET /components/status",
            "GET /workflows",
            "GET /pheromones",
            "POST /pheromones",
            "GET /pheromones/statistics"
        ]
        
        available_enhanced = []
        for endpoint in enhanced_endpoints:
            if endpoint in routes:
                available_enhanced.append(endpoint)
                print(f"      ✅ {endpoint}")
            else:
                print(f"      ❌ {endpoint} missing")
        
        print(f"   📊 Enhanced endpoints: {len(available_enhanced)}/{len(enhanced_endpoints)} available")
        
        return len(available_enhanced) >= len(enhanced_endpoints) * 0.8  # 80% success rate
        
    except Exception as e:
        print(f"   ❌ Enhanced API endpoints test failed: {e}")
        return False

async def test_enhanced_error_handling():
    """Test enhanced error handling and validation"""
    print("🛡️ Testing Enhanced Error Handling...")
    
    try:
        from orchestrator import (
            ProjectRequest, ProjectType, AgentBehavior,
            validate_project_request, AetherforgeError,
            ProjectCreationError, WorkflowExecutionError
        )
        
        print("   ✅ Error handling classes imported")
        
        # Test validation errors
        test_cases = [
            {
                "name": "Short prompt",
                "request": ProjectRequest(prompt="Hi", project_type=ProjectType.FRONTEND),
                "expected_error": "VALIDATION_PROMPT_TOO_SHORT"
            },
            {
                "name": "Long prompt", 
                "request": ProjectRequest(prompt="x" * 10001, project_type=ProjectType.BACKEND),
                "expected_error": "VALIDATION_PROMPT_TOO_LONG"
            },
            {
                "name": "Invalid test coverage",
                "request": ProjectRequest(
                    prompt="Create a test app",
                    project_type=ProjectType.FULLSTACK,
                    test_coverage_target=1.5
                ),
                "expected_error": "VALIDATION_INVALID_TEST_COVERAGE"
            }
        ]
        
        validation_passed = 0
        for test_case in test_cases:
            try:
                await validate_project_request(test_case["request"])
                print(f"      ❌ {test_case['name']}: Should have failed validation")
            except ProjectCreationError as e:
                if test_case["expected_error"] in e.error_code:
                    print(f"      ✅ {test_case['name']}: Correctly rejected ({e.error_code})")
                    validation_passed += 1
                else:
                    print(f"      ⚠️  {test_case['name']}: Wrong error code ({e.error_code})")
            except Exception as e:
                print(f"      ❌ {test_case['name']}: Unexpected error ({type(e).__name__})")
        
        print(f"   📊 Validation tests: {validation_passed}/{len(test_cases)} passed")
        
        # Test custom error creation
        try:
            raise WorkflowExecutionError(
                "Test workflow error",
                error_code="TEST_WORKFLOW_ERROR",
                details={"workflow": "test", "phase": "testing"}
            )
        except WorkflowExecutionError as e:
            print(f"   ✅ Custom error handling works")
            print(f"      Error code: {e.error_code}")
            print(f"      Details: {e.details}")
        
        return validation_passed >= len(test_cases) * 0.8  # 80% success rate
        
    except Exception as e:
        print(f"   ❌ Enhanced error handling test failed: {e}")
        return False

async def main():
    """Main test function for complete enhanced orchestrator"""
    print("🚀 Complete Enhanced Orchestrator Test")
    print("=" * 60)
    
    results = {}
    
    # Test enhanced project creation flow
    results["enhanced_project_creation"] = await test_enhanced_project_creation()
    
    # Test enhanced API endpoints
    results["enhanced_api_endpoints"] = await test_enhanced_api_endpoints()
    
    # Test enhanced error handling
    results["enhanced_error_handling"] = await test_enhanced_error_handling()
    
    # Summary
    print(f"\n📊 Complete Enhanced Test Results:")
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {status} {test_name.replace('_', ' ').title()}")
    
    passed_count = sum(results.values())
    total_count = len(results)
    
    print(f"\nOverall: {passed_count}/{total_count} tests passed ({(passed_count/total_count)*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 ALL ENHANCED FEATURES WORKING PERFECTLY!")
        print("🚀 Enhanced Orchestrator is ready for enterprise-level autonomous software creation!")
    elif passed_count >= total_count * 0.8:
        print("✅ ENHANCED ORCHESTRATOR IS HIGHLY FUNCTIONAL!")
        print("🔧 Minor enhancements may be needed for perfect operation.")
    else:
        print("⚠️  ENHANCED ORCHESTRATOR NEEDS ATTENTION.")
        print("🔧 Some enhanced features require fixes.")
    
    return passed_count >= total_count * 0.8

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
