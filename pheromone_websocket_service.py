# pheromone_websocket_service.py - Real-time WebSocket service for pheromone communication

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Set, Any
import websockets
from websockets.server import WebSocketServerProtocol
import structlog
from pheromone_bus import get_global_bus

# Configure logging
logger = structlog.get_logger()

class PheromoneWebSocketService:
    """WebSocket service for real-time pheromone communication"""
    
    def __init__(self, host: str = "localhost", port: int = 8503):
        self.host = host
        self.port = port
        self.clients: Dict[str, WebSocketServerProtocol] = {}
        self.client_subscriptions: Dict[str, Set[str]] = {}  # client_id -> set of signal filters
        self.pheromone_bus = get_global_bus()
        
        # Subscribe to pheromone bus for real-time notifications
        self.pheromone_bus.subscribe("websocket_service", self._on_pheromone_dropped)
    
    async def start_server(self):
        """Start the WebSocket server"""
        logger.info("Starting pheromone WebSocket service", host=self.host, port=self.port)
        
        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info("Pheromone WebSocket service started", host=self.host, port=self.port)
            await asyncio.Future()  # Run forever
    
    async def handle_client(self, websocket: WebSocketServerProtocol, path: str):
        """Handle a new WebSocket client connection"""
        client_id = str(uuid.uuid4())
        self.clients[client_id] = websocket
        self.client_subscriptions[client_id] = set()
        
        logger.info("Client connected", client_id=client_id, path=path)
        
        try:
            # Send welcome message
            await self._send_to_client(client_id, {
                "type": "welcome",
                "client_id": client_id,
                "timestamp": datetime.now().isoformat()
            })
            
            async for message in websocket:
                await self._handle_message(client_id, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info("Client disconnected", client_id=client_id)
        except Exception as e:
            logger.error("Client error", client_id=client_id, error=str(e))
        finally:
            # Cleanup
            self.clients.pop(client_id, None)
            self.client_subscriptions.pop(client_id, None)
    
    async def _handle_message(self, client_id: str, message: str):
        """Handle a message from a WebSocket client"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "subscribe":
                await self._handle_subscribe(client_id, data)
            elif message_type == "unsubscribe":
                await self._handle_unsubscribe(client_id, data)
            elif message_type == "drop_pheromone":
                await self._handle_drop_pheromone(client_id, data)
            elif message_type == "get_pheromones":
                await self._handle_get_pheromones(client_id, data)
            elif message_type == "get_statistics":
                await self._handle_get_statistics(client_id)
            else:
                await self._send_error(client_id, f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            await self._send_error(client_id, "Invalid JSON message")
        except Exception as e:
            logger.error("Message handling error", client_id=client_id, error=str(e))
            await self._send_error(client_id, f"Internal error: {str(e)}")
    
    async def _handle_subscribe(self, client_id: str, data: Dict[str, Any]):
        """Handle subscription request"""
        signal_filter = data.get("signal_filter")
        
        if signal_filter:
            self.client_subscriptions[client_id].add(signal_filter)
        else:
            self.client_subscriptions[client_id].add("*")  # Subscribe to all
        
        await self._send_to_client(client_id, {
            "type": "subscription_confirmed",
            "signal_filter": signal_filter,
            "timestamp": datetime.now().isoformat()
        })
        
        logger.info("Client subscribed", client_id=client_id, signal_filter=signal_filter)
    
    async def _handle_unsubscribe(self, client_id: str, data: Dict[str, Any]):
        """Handle unsubscription request"""
        signal_filter = data.get("signal_filter")
        
        if signal_filter:
            self.client_subscriptions[client_id].discard(signal_filter)
        else:
            self.client_subscriptions[client_id].clear()
        
        await self._send_to_client(client_id, {
            "type": "unsubscription_confirmed",
            "signal_filter": signal_filter,
            "timestamp": datetime.now().isoformat()
        })
        
        logger.info("Client unsubscribed", client_id=client_id, signal_filter=signal_filter)
    
    async def _handle_drop_pheromone(self, client_id: str, data: Dict[str, Any]):
        """Handle pheromone drop request"""
        try:
            signal = data.get("signal")
            payload = data.get("payload", {})
            project_id = data.get("project_id")
            agent_id = data.get("agent_id")
            trail_id = data.get("trail_id")
            priority = data.get("priority", 5)
            
            if not signal:
                await self._send_error(client_id, "Signal is required")
                return
            
            pheromone_id = self.pheromone_bus.drop_pheromone(
                signal=signal,
                payload=payload,
                project_id=project_id,
                agent_id=agent_id,
                trail_id=trail_id,
                priority=priority
            )
            
            await self._send_to_client(client_id, {
                "type": "pheromone_dropped",
                "pheromone_id": pheromone_id,
                "signal": signal,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            await self._send_error(client_id, f"Failed to drop pheromone: {str(e)}")
    
    async def _handle_get_pheromones(self, client_id: str, data: Dict[str, Any]):
        """Handle get pheromones request"""
        try:
            signal = data.get("signal")
            project_id = data.get("project_id")
            agent_id = data.get("agent_id")
            trail_id = data.get("trail_id")
            limit = data.get("limit", 100)
            
            pheromones = self.pheromone_bus.get_pheromones(
                signal=signal,
                project_id=project_id,
                agent_id=agent_id,
                trail_id=trail_id,
                limit=limit
            )
            
            await self._send_to_client(client_id, {
                "type": "pheromones_response",
                "pheromones": pheromones,
                "count": len(pheromones),
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            await self._send_error(client_id, f"Failed to get pheromones: {str(e)}")
    
    async def _handle_get_statistics(self, client_id: str):
        """Handle get statistics request"""
        try:
            stats = self.pheromone_bus.get_statistics()
            stats["websocket_clients"] = len(self.clients)
            
            await self._send_to_client(client_id, {
                "type": "statistics_response",
                "statistics": stats,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            await self._send_error(client_id, f"Failed to get statistics: {str(e)}")
    
    async def _send_to_client(self, client_id: str, data: Dict[str, Any]):
        """Send data to a specific client"""
        if client_id in self.clients:
            try:
                await self.clients[client_id].send(json.dumps(data))
            except websockets.exceptions.ConnectionClosed:
                # Client disconnected, remove from clients
                self.clients.pop(client_id, None)
                self.client_subscriptions.pop(client_id, None)
            except Exception as e:
                logger.error("Failed to send to client", client_id=client_id, error=str(e))
    
    async def _send_error(self, client_id: str, error_message: str):
        """Send an error message to a client"""
        await self._send_to_client(client_id, {
            "type": "error",
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        })
    
    def _on_pheromone_dropped(self, pheromone: Dict[str, Any]):
        """Callback for when a pheromone is dropped - notify relevant clients"""
        signal = pheromone.get("signal")
        
        # Prepare notification message
        notification = {
            "type": "pheromone_notification",
            "pheromone": pheromone,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to all relevant clients
        for client_id, subscriptions in self.client_subscriptions.items():
            if "*" in subscriptions or signal in subscriptions:
                asyncio.create_task(self._send_to_client(client_id, notification))

async def main():
    """Main function to start the WebSocket service"""
    service = PheromoneWebSocketService()
    await service.start_server()

if __name__ == "__main__":
    asyncio.run(main())
