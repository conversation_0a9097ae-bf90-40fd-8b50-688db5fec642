#!/usr/bin/env python3
"""
Aetherforge System Validation Script

This script validates that the complete Aetherforge system is working correctly.
It performs end-to-end testing of the project creation workflow.
"""

import os
import sys
import time
import json
import requests
import subprocess
from pathlib import Path
from typing import Dict, List, Optional


class AetherforgeValidator:
    """Validates the Aetherforge system"""
    
    def __init__(self, orchestrator_url: str = "http://localhost:8000"):
        self.orchestrator_url = orchestrator_url
        self.test_results: Dict[str, bool] = {}
        self.test_details: Dict[str, str] = {}
    
    def log(self, message: str, level: str = "INFO"):
        """Log a message with timestamp"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_orchestrator_health(self) -> bool:
        """Test orchestrator health endpoint"""
        self.log("Testing orchestrator health...")
        
        try:
            response = requests.get(f"{self.orchestrator_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    self.log("✅ Orchestrator is healthy")
                    return True
                else:
                    self.log(f"❌ Orchestrator unhealthy: {data}")
                    return False
            else:
                self.log(f"❌ Orchestrator health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ Failed to connect to orchestrator: {e}")
            return False
    
    def test_component_status(self) -> bool:
        """Test component status endpoint"""
        self.log("Testing component status...")
        
        try:
            response = requests.get(f"{self.orchestrator_url}/components/status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                components = data.get("components", {})
                
                self.log(f"Component status: {components}")
                
                # Check that orchestrator is running
                if components.get("orchestrator") == "running":
                    self.log("✅ Component status check passed")
                    return True
                else:
                    self.log("⚠️  Some components may be offline, but orchestrator is running")
                    return True
            else:
                self.log(f"❌ Component status check failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ Failed to check component status: {e}")
            return False
    
    def test_pheromone_system(self) -> bool:
        """Test pheromone system"""
        self.log("Testing pheromone system...")
        
        try:
            # Drop a test pheromone
            pheromone_data = {
                "signal": "validation_test",
                "payload": {"test": True, "timestamp": time.time()},
                "project_id": "validation_project"
            }
            
            response = requests.post(
                f"{self.orchestrator_url}/pheromones",
                json=pheromone_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "success":
                    self.log("✅ Pheromone system working")
                    return True
                else:
                    self.log(f"❌ Pheromone drop failed: {result}")
                    return False
            else:
                self.log(f"❌ Pheromone system test failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ Failed to test pheromone system: {e}")
            return False
    
    def test_project_creation(self) -> bool:
        """Test project creation workflow"""
        self.log("Testing project creation...")
        
        try:
            project_data = {
                "prompt": "Create a simple hello world web application with a single page that displays 'Hello, World!'",
                "project_name": "ValidationTest",
                "project_type": "fullstack"
            }
            
            self.log("Sending project creation request...")
            response = requests.post(
                f"{self.orchestrator_url}/projects",
                json=project_data,
                timeout=120  # Allow 2 minutes for project creation
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "success":
                    project_id = result.get("project_id")
                    project_slug = result.get("project_slug")
                    
                    self.log(f"✅ Project created successfully: {project_slug} ({project_id})")
                    
                    # Check if project files were created
                    project_path = result.get("project_path")
                    if project_path and Path(project_path).exists():
                        self.log(f"✅ Project files created at: {project_path}")
                        return True
                    else:
                        self.log("⚠️  Project created but files not found locally")
                        return True
                else:
                    self.log(f"❌ Project creation failed: {result}")
                    return False
            else:
                self.log(f"❌ Project creation request failed: {response.status_code}")
                try:
                    error_data = response.json()
                    self.log(f"Error details: {error_data}")
                except:
                    self.log(f"Response text: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Failed to test project creation: {e}")
            return False
    
    def test_api_documentation(self) -> bool:
        """Test API documentation endpoint"""
        self.log("Testing API documentation...")
        
        try:
            response = requests.get(f"{self.orchestrator_url}/docs", timeout=10)
            
            if response.status_code == 200:
                self.log("✅ API documentation accessible")
                return True
            else:
                self.log(f"❌ API documentation not accessible: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ Failed to access API documentation: {e}")
            return False
    
    def test_projects_list(self) -> bool:
        """Test projects list endpoint"""
        self.log("Testing projects list...")
        
        try:
            response = requests.get(f"{self.orchestrator_url}/projects", timeout=10)
            
            if response.status_code == 200:
                projects = response.json()
                self.log(f"✅ Projects list accessible ({len(projects)} projects)")
                return True
            else:
                self.log(f"❌ Projects list not accessible: {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ Failed to access projects list: {e}")
            return False
    
    def check_docker_services(self) -> bool:
        """Check if Docker services are running"""
        self.log("Checking Docker services...")
        
        try:
            # Check if docker-compose is available
            result = subprocess.run(
                ["docker-compose", "ps"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                if "orchestrator" in output:
                    self.log("✅ Docker services detected")
                    return True
                else:
                    self.log("⚠️  Docker Compose available but Aetherforge services not detected")
                    return False
            else:
                self.log("⚠️  Docker Compose not available or no services running")
                return False
                
        except Exception as e:
            self.log(f"⚠️  Could not check Docker services: {e}")
            return False
    
    def run_validation(self) -> bool:
        """Run complete system validation"""
        self.log("🔮 Starting Aetherforge System Validation")
        self.log("=" * 60)
        
        tests = [
            ("Docker Services", self.check_docker_services),
            ("Orchestrator Health", self.test_orchestrator_health),
            ("Component Status", self.test_component_status),
            ("API Documentation", self.test_api_documentation),
            ("Projects List", self.test_projects_list),
            ("Pheromone System", self.test_pheromone_system),
            ("Project Creation", self.test_project_creation),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            self.log(f"\n🧪 Running test: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                self.log(f"❌ Test {test_name} failed with exception: {e}")
                self.test_results[test_name] = False
        
        # Print summary
        self.log("\n" + "=" * 60)
        self.log("📊 Validation Summary")
        self.log(f"✅ Passed: {passed}/{total}")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"  {test_name}: {status}")
        
        if passed == total:
            self.log("\n🎉 All validation tests passed!")
            self.log("Aetherforge system is working correctly!")
            return True
        else:
            self.log(f"\n❌ {total - passed} validation test(s) failed")
            self.log("Please check the system configuration and try again.")
            return False
    
    def generate_report(self, output_file: str = "validation_report.json"):
        """Generate validation report"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "orchestrator_url": self.orchestrator_url,
            "test_results": self.test_results,
            "test_details": self.test_details,
            "summary": {
                "total_tests": len(self.test_results),
                "passed_tests": sum(1 for result in self.test_results.values() if result),
                "failed_tests": sum(1 for result in self.test_results.values() if not result),
                "success_rate": sum(1 for result in self.test_results.values() if result) / len(self.test_results) if self.test_results else 0
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.log(f"📄 Validation report saved to: {output_file}")


def main():
    """Main validation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Aetherforge System Validator")
    parser.add_argument(
        "--url",
        default="http://localhost:8000",
        help="Orchestrator URL (default: http://localhost:8000)"
    )
    parser.add_argument(
        "--report",
        default="validation_report.json",
        help="Output file for validation report"
    )
    
    args = parser.parse_args()
    
    validator = AetherforgeValidator(args.url)
    
    try:
        success = validator.run_validation()
        validator.generate_report(args.report)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        validator.log("\n⚠️  Validation interrupted by user")
        sys.exit(1)


if __name__ == "__main__":
    main()
